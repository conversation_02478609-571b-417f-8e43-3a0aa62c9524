{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:webpack": "next dev", "dev:trace": "NEXT_TURBOPACK_TRACING=1 next dev --turbopack", "build": "pnpm run partytown && node scripts/generate-sitemap-data.js && next build && node scripts/generate-critical-css.js && cp -r public .next/standalone/", "partytown": "partytown copylib public/~partytown", "start": "node .next/standalone/server.js", "type-check": "tsc --noEmit", "test": "echo 'No tests configured yet' && exit 0", "knip": "knip", "knip:production": "knip --production", "security:audit": "pnpm audit --audit-level moderate", "analyze": "ANALYZE=true next build", "analyze:server": "ANALYZE=true BUNDLE_ANALYZE=server next build", "analyze:browser": "ANALYZE=true BUNDLE_ANALYZE=browser next build", "analyze:both": "ANALYZE=true BUNDLE_ANALYZE=both next build", "lint": "biome lint --max-diagnostics=2000 .", "lint:trace": "biome lint --log-level=tracing --log-kind=json --log-file=tracing.json --max-diagnostics=2000 .", "format": "biome format --write .", "check": "biome check --write --max-diagnostics=2000 .", "check:trace": "biome check --log-level=tracing --log-kind=json --log-file=tracing.json --max-diagnostics=2000 .", "validate-analytics": "node scripts/validate-analytics-env.js"}, "dependencies": {"@c15t/nextjs": "1.5.0-canary-20250709081316", "@next/third-parties": "15.3.5", "@qwik.dev/partytown": "^0.11.1", "@radix-ui/react-dialog": "1.1.14", "@types/iframe-resizer": "^4.0.0", "@vercel/analytics": "1.5.0", "@vercel/speed-insights": "1.2.0", "autoprefixer": "10.4.21", "clsx": "2.1.1", "framer-motion": "^12.23.0", "iframe-resizer": "^4.4.5", "lucide-react": "0.525.0", "next": "15.3.5", "next-themes": "0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-google-reviews": "^1.7.4", "tailwind-merge": "3.3.1", "vercel": "^44.4.1"}, "devDependencies": {"@biomejs/biome": "2.1.1", "@next/bundle-analyzer": "15.3.5", "@tailwindcss/postcss": "4.1.11", "@types/node": "24.0.11", "@types/react": "19.1.3", "@types/react-dom": "19.1.3", "beasties": "^0.3.5", "crypto-js": "^4.2.0", "knip": "^5.61.3", "node-html-parser": "^7.0.1", "postcss": "8.5.6", "postcss-load-config": "^6.0.1", "tailwindcss": "4.1.11", "typescript": "5.8.3"}, "resolutions": {"rollup": "^2.79.1", "rollup-plugin-terser": "^7.0.2", "d3-path": "^3.1.0", "@types/react": "19.1.3", "@types/react-dom": "19.1.3", "brace-expansion": "^4.0.1", "undici": "5.29.0", "esbuild": "0.25.0", "next": "15.3.5"}, "overrides": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/pg": "8.6.0", "brace-expansion": "^4.0.1", "http-proxy": "npm:@cypress/http-proxy@^1.18.1", "undici": "5.29.0", "esbuild": "0.25.0", "@doubletie/logger": {"next": "15.3.5"}}}