name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  quality-checks:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Cache Biome
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/biome
            .biome-cache
          key: biome-${{ runner.os }}-${{ hashFiles('biome.json', 'package.json') }}
          restore-keys: |
            biome-${{ runner.os }}-

      - name: Biome check (format + lint)
        run: pnpm check

      - name: Type check
        run: pnpm type-check

      - name: Run Knip (unused code detection)
        run: pnpm knip
        continue-on-error: true

      - name: Security audit
        run: pnpm security:audit

  build:
    runs-on: ubuntu-latest
    needs: quality-checks

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build application
        run: pnpm build

      - name: Verify build output
        run: |
          echo "Checking build output..."
          ls -la .next/
          if [ -f ".next/standalone/server.js" ]; then
            echo "✅ Standalone build successful"
          else
            echo "❌ Standalone build failed"
            exit 1
          fi

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-files
          path: |
            .next/standalone/
            .next/static/
            public/
