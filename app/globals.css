@import "tailwindcss";

/* Optimized source paths for better tree-shaking and performance */
@source '../components/ui/**/*.{js,ts,jsx,tsx}';
@source '../components/layout/**/*.{js,ts,jsx,tsx}';
@source '../components/**/*.{js,ts,jsx,tsx}';
@source '../app/**/page.{js,ts,jsx,tsx}';
@source '../app/**/layout.{js,ts,jsx,tsx}';
@source '../app/**/*.{js,ts,jsx,tsx}';
@source '../lib/utils.{js,ts}';
@source '../lib/**/*.{js,ts,jsx,tsx}';
@source '../contexts/**/*.{js,ts,jsx,tsx}';
@source '../hooks/**/*.{js,ts,jsx,tsx}';

/* Include specific high-usage component patterns */
@source '../**/*{<PERSON><PERSON>,<PERSON>,<PERSON>,Form,Input,Modal}*.{js,ts,jsx,tsx}';

@custom-variant dark (&:is(.dark *));

/* c15t Cookie Consent Banner Styling */
/* Custom styling to match Pony Club vintage design system */

.cookie-consent-banner {
  font-family: inherit;
  z-index: 9999;
}

/* Banner container styling */
.c15t-banner {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
  margin: 1rem;
  max-width: 600px;
}

/* Banner text styling */
.c15t-banner-title {
  color: #6b8362;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.c15t-banner-description {
  color: #4b5563;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

/* Button styling to match your design system */
.c15t-button {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: none;
  margin: 0 0.25rem;
}

.c15t-button-primary {
  background-color: #6b8362;
  color: white;
}

.c15t-button-primary:hover {
  background-color: #5a6f52;
  transform: translateY(-1px);
}

.c15t-button-secondary {
  background-color: transparent;
  color: #6b8362;
  border: 1px solid #6b8362;
}

.c15t-button-secondary:hover {
  background-color: #6b8362;
  color: white;
}

.c15t-button-tertiary {
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #d1d5db;
}

.c15t-button-tertiary:hover {
  background-color: #e5e7eb;
}

/* Settings modal styling */
.c15t-modal {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.c15t-modal-content {
  background: white;
  border-radius: 8px;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.c15t-modal-header {
  padding: 1.5rem 1.5rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.c15t-modal-title {
  color: #6b8362;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.c15t-modal-body {
  padding: 1.5rem;
}

.c15t-category {
  margin-bottom: 1.5rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.c15t-category-title {
  color: #374151;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.c15t-category-description {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Toggle switch styling */
.c15t-toggle {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.c15t-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.c15t-toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #d1d5db;
  transition: 0.2s;
  border-radius: 24px;
}

.c15t-toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.2s;
  border-radius: 50%;
}

.c15t-toggle input:checked + .c15t-toggle-slider {
  background-color: #6b8362;
}

.c15t-toggle input:checked + .c15t-toggle-slider:before {
  transform: translateX(20px);
}

/* Responsive design */
@media (max-width: 640px) {
  .c15t-banner {
    margin: 0.5rem;
    padding: 1rem;
  }

  .c15t-button {
    display: block;
    width: 100%;
    margin: 0.25rem 0;
  }

  .c15t-modal-content {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }
}

@layer base {
  :root {
    --breakpoint-xs: 480px;
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
  @media (width >= 475px) {
    max-width: none;
  }
  @media (width >= 1400px) {
    max-width: 1400px;
  }
}

@utility text-balance {
  text-wrap: balance;
}

/* Container Query Utilities - New in Tailwind v4 */
@utility responsive-card {
  container-type: inline-size;

  /* Small container */
  @container (min-width: 300px) {
    padding: 1.5rem;

    & .card-title {
      font-size: 1.25rem;
    }
  }

  /* Medium container */
  @container (min-width: 500px) {
    padding: 2rem;

    & .card-title {
      font-size: 1.5rem;
    }

    & .card-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
    }
  }

  /* Large container */
  @container (min-width: 700px) {
    padding: 2.5rem;

    & .card-title {
      font-size: 1.875rem;
    }

    & .card-content {
      grid-template-columns: 1fr 1fr 1fr;
    }
  }
}

@utility container-card {
  container-type: inline-size;
  border-radius: var(--radius-lg);
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  padding: 1rem;

  @container (min-width: 400px) {
    padding: 1.5rem;
  }

  @container (min-width: 600px) {
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
  }
}

/* 3D Transform Utilities - Enhanced in Tailwind v4 */
@utility card-3d {
  transform-style: preserve-3d;
  perspective: 1000px;
  transition: transform 0.3s ease;

  &:hover {
    transform: perspective(1000px) rotateX(5deg) rotateY(5deg) translateZ(20px);
  }
}

@utility flip-card {
  transform-style: preserve-3d;
  transition: transform 0.6s;

  &:hover {
    transform: rotateY(180deg);
  }

  & .flip-card-front,
  & .flip-card-back {
    backface-visibility: hidden;
    position: absolute;
    inset: 0;
  }

  & .flip-card-back {
    transform: rotateY(180deg);
  }
}

@utility floating-3d {
  transform: perspective(1000px) translateZ(0);
  transition: transform 0.3s ease;

  &:hover {
    transform: perspective(1000px) translateZ(30px) rotateX(10deg);
  }
}

@utility parallax-3d {
  transform-style: preserve-3d;

  & .parallax-layer-1 {
    transform: translateZ(-100px) scale(1.1);
  }

  & .parallax-layer-2 {
    transform: translateZ(-200px) scale(1.2);
  }

  & .parallax-layer-3 {
    transform: translateZ(-300px) scale(1.3);
  }
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@utility program-card {
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("/pattern.svg");
    opacity: 0.18;
    mix-blend-mode: overlay;
    pointer-events: none;
  }
}
@utility text-shadow-lg {
  /* Enhanced text shadow utilities using theme variables */
  text-shadow: var(--text-shadow-lg);
}

@utility text-shadow-xl {
  text-shadow: var(--text-shadow-xl);
}

/* New enhanced 3D text utilities */
@utility text-3d {
  text-shadow:
    1px 1px 0 hsl(var(--muted)),
    2px 2px 0 hsl(var(--muted)),
    3px 3px 0 hsl(var(--muted)),
    4px 4px 8px rgba(0, 0, 0, 0.3);
  transform: perspective(500px) rotateX(15deg);
}

@utility text-glow {
  text-shadow:
    0 0 5px hsl(var(--river-accent)),
    0 0 10px hsl(var(--river-accent)),
    0 0 15px hsl(var(--river-accent)),
    0 0 20px hsl(var(--river-accent));
}
@utility animate-text-shine {
  /* Text shine animation */
  background-size: 200% auto;
  animation: textShine 4s ease-in-out infinite;
}
@utility gradient-text {
  /* Gradient text for headings */
  background: linear-gradient(90deg, hsl(var(--river-accent)), #6b8362);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}
@utility frosted-card {
  /* Enhanced frosted glass card with modern CSS features */
  position: relative;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(45deg, hsl(var(--river-accent)), #6b8362);
    -webkit-mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    mask-composite: exclude;
  }
}

/* Modern CSS Features - OKLCH Colors and Advanced Utilities */
@utility oklch-primary {
  /* Using OKLCH for better color consistency across devices */
  background: oklch(70% 0.15 250);
  color: oklch(95% 0.02 250);
}

@utility oklch-accent {
  background: oklch(75% 0.2 200);
  color: oklch(20% 0.05 200);
}

@utility oklch-success {
  background: oklch(70% 0.15 140);
  color: oklch(95% 0.02 140);
}

@utility oklch-warning {
  background: oklch(80% 0.15 60);
  color: oklch(20% 0.05 60);
}

@utility oklch-error {
  background: oklch(65% 0.2 20);
  color: oklch(95% 0.02 20);
}

/* Advanced Layout Utilities */
@utility grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

@utility grid-auto-fill {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

@utility subgrid-rows {
  display: subgrid;
  grid-template-rows: subgrid;
}

@utility subgrid-columns {
  display: subgrid;
  grid-template-columns: subgrid;
}

/* Modern Animation Utilities */
@utility animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@utility animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

@utility animate-bounce-in {
  animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@layer base {
  :root {
    --background: 40 50% 97%; /* Light warm off-white */
    --foreground: 90 25% 12%; /* Darker green for better contrast */

    --card: 0 0% 100%; /* White */
    --card-foreground: 90 25% 12%; /* Darker green text on cards */

    --popover: 0 0% 100%; /* White */
    --popover-foreground: 90 25% 12%; /* Darker green text on popovers */

    --primary: 90 30% 30%; /* Darker earthy green for better contrast */
    --primary-foreground: 40 50% 97%; /* Light text on primary */

    --secondary: 40 50% 92%; /* Slightly darker beige */
    --secondary-foreground: 90 30% 25%; /* Darker green text on secondary */

    --muted: 40 40% 88%; /* Muted beige */
    --muted-foreground: 90 25% 25%; /* Darker green text for better contrast */

    --accent: 30 70% 60%; /* Warm amber */
    --accent-foreground: 0 0% 100%; /* White text on accent */

    --river-accent: 204 95% 45%; /* Existing distinct blue, can be reviewed later */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%; /* White text on destructive */

    --border: 40 30% 80%; /* Soft beige border */
    --input: 40 30% 80%; /* Soft beige input border */
    --ring: 30 70% 60%; /* Amber for focus rings */

    --radius: 0.5rem;
  }

  .dark {
    --background: 90 15% 10%; /* Very dark green */
    --foreground: 40 50% 90%; /* Light beige text */

    --card: 90 15% 12%; /* Dark green cards */
    --card-foreground: 40 50% 90%; /* Light beige text on cards */

    --popover: 90 15% 12%; /* Dark green popovers */
    --popover-foreground: 40 50% 90%; /* Light beige text on popovers */

    --primary: 90 30% 55%; /* Lighter green for dark mode */
    --primary-foreground: 90 15% 10%; /* Dark text on primary */

    --secondary: 90 15% 15%; /* Slightly lighter dark green */
    --secondary-foreground: 40 50% 90%; /* Light beige text on secondary */

    --muted: 90 15% 20%;
    --muted-foreground: 40 40% 70%;

    --accent: 30 70% 70%; /* Lighter amber for dark mode */
    --accent-foreground: 90 15% 10%; /* Dark text on accent */

    --river-accent: 204 95% 55%; /* Slightly lighter blue for dark mode */

    --destructive: 0 70% 50%; /* Adjusted destructive for dark mode */
    --destructive-foreground: 0 0% 100%; /* White text on destructive */

    --border: 90 15% 25%;
    --input: 90 15% 25%;
    --ring: 30 70% 70%; /* Lighter amber ring for dark mode */
  }
}

@layer base {
  * {
    @apply border-[--border] transition-colors duration-300 ease-in-out; /* Added global transition */
  }
  body {
    @apply bg-[--background] text-[--foreground];
    font-feature-settings:
      "liga" 1,
      "calt" 1; /* Enable ligatures and contextual alternates */
  }
}

/* Enhanced keyframes for modern animations */
@layer components {
  @keyframes textShine {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes bounceIn {
    0% {
      transform: scale(0.3);
      opacity: 0;
    }
    50% {
      transform: scale(1.05);
    }
    70% {
      transform: scale(0.9);
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
}
