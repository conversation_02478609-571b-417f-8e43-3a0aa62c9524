---
title: useConsentManager
description: The useConsentManager hook provides access to the complete consent management API, allowing you to interact with and control the consent state throughout your application.
---
import UseConsentManagerExample from '~/examples/react/use-consent-manager';

## Usage `useConsentManager`

This example shows the hook being used to re-open the cookie banner once it has been closed. This could be used to re-open the banner after a certain amount of time has passed, or when a user wishes to update their consents for example.

<UseConsentManagerExample/>

## Returns

The hook returns an object containing both state properties and methods for managing consent:

<auto-type-table path="../../node_modules/c15t/src/index.ts" name="PrivacyConsentState" />

## Notes

- The hook must be used within a `ConsentManagerProvider` component
- Throws an error if used outside of a `ConsentManagerProvider`
- All methods are memoized and safe to use in effects or callbacks
- Changes to consent state are automatically persisted
- Supports TypeScript with full type safety
