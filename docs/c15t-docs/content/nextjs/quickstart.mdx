---
title: Next.js Quickstart
description: Learn how to integrate c15t into your Next.js application with this step-by-step guide. We'll cover installation, configuration, and basic usage.
---
import { RiFileCopyLine, RiFlashlightLine, RiServerLine, RiWifiOffLine, RiSettings4Line } from '@remixicon/react';

## CLI Setup (Recommended)
<Steps>

<Step>
### Generate Schema + Code

<RunCommand command="@c15t/cli generate" />

</Step>

<Step>
### Run Database Migrations (Optional)

<Callout type="note">
This is only required if you are self hosting c15t.
</Callout>

<RunCommand command="@c15t/cli migrate" />

</Step>
</Steps>

## Manual Setup
<Steps>
<Step>
 
### Install `@c15t/nextjs` Package
 ```package-install
 @c15t/nextjs
 ```
</Step>

<Step>
 
### Next.js Rewrites (Optional)

<Callout type="tip">
You can use Next.js Rewrites to redirect requests to the c15t backend. This is useful if you want to hide the c15t backend url from your users.
[Learn more about Next.js Rewrites](https://nextjs.org/docs/app/api-reference/config/next-config-js/rewrites).
</Callout>

```ts title="next.config.ts"
import type { NextConfig } from 'next';

const config: NextConfig = {
	async rewrites() {
		return [
			{
				source: '/api/c15t/:path*',
				destination: `${process.env.NEXT_PUBLIC_C15T_URL}/:path*`,
			},
		];
	},
};

export default config;
```
</Step>
 
<Step>
 
### Adding it to your Next Application

```tsx title="app/layout.tsx"
import { 
  ConsentManagerDialog,
  ConsentManagerProvider,
  CookieBanner,
} from '@c15t/nextjs';

export default function Layout ({ children }: { children: ReactNode }) => {
	return (
      <ConsentManagerProvider
        options={{
          mode: 'c15t',
          backendURL: '/api/c15t',
          consentCategories: ['necessary', 'marketing'], // Optional: Specify which consent categories to show in the banner.
          ignoreGeoLocation: true, // Useful for development to always view the banner.
        }}
      >
        <CookieBanner />
        <ConsentManagerDialog />   
        {children}
      </ConsentManagerProvider>
  );
};
```

<Callout type="tip">
You can create an instance at [consent.io](https://consent.io) or self-host your own instance. Otherwise, you can use c15t offline by setting `mode: 'offline'`.
</Callout>

<Callout type="tip">
  If you're using Next.js Rewrites, you can use the `backendURL` option to redirect requests to the c15t backend by setting it to `/api/c15t`.
</Callout>
</Step>
</Steps>

--- 

## Hosting Options

### Creating a consent.io Instance (Recommended)

<Callout type="info">
Using consent.io is the recommended method as it is the easiest way to get started and requires little maintenance.
</Callout>

Instead of self-hosting your own c15t instance, you can use a [consent.io](https://consent.io) instance. This is the recommended method as it is the easiest way to get started and requires little maintenance.

<Steps>

<Step>
Sign up for a [consent.io](https://consent.io) account.
</Step>

<Step>
After signing up, create a new instance, located in the top-right corner.

<Callout>
When creating an instance it is important to list all the trusted origins for your application such as "localhost", "vercel.app", "c15t.com" etc.
</Callout>

</Step>

<Step>
After the instance is created, you will be given a backendURL, which you can add to your `ConsentManagerOptions`.


A backend URL might look like this: `https://<my-instance>.c15t.dev/`.
</Step>

</Steps>

### Alternative Hosting Options

<Callout type="note">
For more advanced setup options, choose the approach that best suits your infrastructure and requirements.
</Callout>

For more advanced setup options, please refer to:
- [Overview](/docs/storing-consent/overview) - Compare different approaches to storing consent decisions in your application
- [Hosted c15t](/docs/storing-consent/hosted-c15t) - Complete guide to using consent.io
- [Self-Hosting](/docs/storing-consent/self-hosting) - Run your own c15t instance 
- [Offline Mode](/docs/storing-consent/offline-mode) - Complete guide to using c15t without a backend
- [Custom Client](/docs/storing-consent/custom-client) - Advanced implementation with custom handlers for full control


## Decision Guide

<Callout>
Use this flowchart to determine which c15t configuration is best for your needs.
</Callout>

Use this flowchart to determine which c15t configuration is best for your needs:

<Mermaid chart={`
flowchart TD
    Start([Start here]) --> StoreConsent
    
    StoreConsent{Need to store\nconsent choices?}
    StoreConsent -->|Yes| ManagedService
    StoreConsent -->|No| OfflineMode
    
    ManagedService{Want a managed\nservice?}
    ManagedService -->|Yes| ConsentIO
    ManagedService -->|No| SelfHosted
    
    OfflineMode([c15t Offline Mode]):::optionStyle
    OfflineMode -.-> OfflineNote[Client-side only\nStores in localStorage]:::noteStyle
    
    ConsentIO([consent.io]):::recommendStyle
    ConsentIO -.-> ConsentIONote[Fully managed\nSimplest setup]:::noteStyle
    
    SelfHosted([Self-Hosted Instance]):::optionStyle
    SelfHosted -.-> SelfHostedNote[Full control\nRequires maintenance]:::noteStyle
    
    %% Styling
    classDef recommendStyle fill:#4caf50,stroke:#388e3c,color:white,stroke-width:2px;
    classDef optionStyle fill:#37474f,stroke:#263238,color:white,stroke-width:1px;
    classDef noteStyle fill:#424242,stroke:none,color:#aaa,font-size:12px;
`} />

## Next Steps in This Guide

<Callout type="tip">
Choose your next step based on your specific implementation needs.
</Callout>

<div className="mt-6">
  <CompactCard 
    href="/docs/storing-consent/overview" 
    icon={<RiFileCopyLine size={16} />}
  >
Learn more about <code>Client Configuration Options</code>
  </CompactCard>

  <CompactCard 
    href="/docs/storing-consent/hosted-c15t"
    icon={<RiFlashlightLine size={16} />}
  >
    Learn more about <code>Hosted c15t</code>
  </CompactCard>

  <CompactCard 
    href="/docs/storing-consent/self-hosting"
    icon={<RiServerLine size={16} />}
  >
    Learn more about <code>Self-Hosting</code>
  </CompactCard>

  <CompactCard 
    href="/docs/storing-consent/offline-mode"
    icon={<RiWifiOffLine size={16} />}
  >
    Learn more about <code>Offline Mode</code>
  </CompactCard>

  <CompactCard 
    href="/docs/storing-consent/custom-client"
    icon={<RiSettings4Line size={16} />}
  >
    Learn more about <code>Custom Client</code>
  </CompactCard>
</div>

