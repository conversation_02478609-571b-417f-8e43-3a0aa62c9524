{"title": "NextJS", "root": true, "icon": "next", "pages": ["../introduction", "../ai-tools-integrations", "quickstart", "google-tag-manager", "../callbacks", "--- React Components ---", "../components/react/cookie-banner", "../components/react/consent-manager-dialog", "../components/react/consent-manager-widget", "../components/react/dev-tools", "--- <PERSON><PERSON> Consent ---", "../storing-consent/overview", "../storing-consent/hosted-c15t", "../storing-consent/self-hosting", "../storing-consent/offline-mode", "../storing-consent/custom-client", "--- Hooks ---", "../hooks/use-consent-manager", "../hooks/use-focus-trap", "--- Styling the components ---", "../styling/index", "../styling/color-scheme", "../styling/classnames", "../styling/tailwind", "../styling/css-variables", "../styling/inline-styles", "--- Self-Hosting ---", "../backend/index", "../backend/core-concepts", "../backend/database-adapters", "../backend/plugins", "--- Databases ---", "../backend/databases/postgres", "../backend/databases/mysql", "../backend/databases/sqlite", "--- Database Adapters ---", "../backend/adapters/memory", "../backend/adapters/kysely", "../backend/adapters/prisma", "../backend/adapters/drizzle", "--- Contributing ---", "../oss/contributing", "../oss/license", "../oss/why-open-source"]}