---
title: Dev Tool
description: Monitor and debug your privacy consent management with our development tools, designed to help you build with confidence.
---

Development tools provide real-time insight into how users interact with your privacy consent management system. With live state monitoring, intuitive debugging features, and customizable visualization options, you can ensure your privacy implementation works exactly as intended.

## Understanding Dev Tools

When building privacy-compliant applications, you need visibility into how consent is being managed. Our dev tools let you:

- Watch consent changes in real-time as users interact with your privacy controls
- Inspect the complete consent state at any point in time
- Debug issues by tracking consent management flow
- Validate that your privacy implementation works correctly

Think of it like having a privacy-focused browser dev tools panel - but specifically designed for consent management.

## Adding Dev Tools to Your Project

First, install the development tools package:

{/* ```package-install
@c15t/dev-tools
``` */}


Then add the development tools to your application:

```tsx
import { C15TDevTools } from '@c15t/dev-tools';

function App() {
  return (
    <>
      <YourApp />
      {process.env.NODE_ENV === 'development' && <C15TDevTools />}
    </>
  )
}
```

The dev tools will automatically appear in your application during development. They're designed to be unobtrusive while still providing quick access to important information.


## API Reference

### PrivacyDevTools

The main development tools component accepts these props:

<auto-type-table path="../../../node_modules/@c15t/dev-tools/src/dev-tool.tsx" name="ConsentManagerProviderProps" />


## Development Best Practices

To get the most out of the development tools:

1. Keep dev tools enabled during development and testing
   - They help catch issues early
   - Make debugging more efficient
   - Provide insights into user behavior

2. Use the inspection APIs to validate consent handling
   - Verify consents are saved correctly
   - Check that preferences persist properly
   - Ensure privacy choices are respected

3. Monitor consent flow during testing
   - Track the user journey through consent management
   - Identify potential UX improvements
   - Validate compliance requirements

4. Remove dev tools in production
   ```tsx
   // Automatically removed in production builds
   {process.env.NODE_ENV === 'development' && <C15TDevTools />}
   ```

## Security Considerations

When using the development tools, keep in mind:

- Dev tools may expose sensitive user preferences
- Always remove dev tools before deploying to production
- Be cautious when logging consent state
- Consider privacy implications when sharing debug information


## Troubleshooting Common Issues

If you encounter problems:

1. Verify the dev tools are only running in development
2. Check that your privacy components are properly initialized
3. Ensure the consent state namespace matches your configuration
4. Look for error messages in the browser console

Remember, the development tools are here to help you build better privacy experiences. Use them to understand, debug, and improve your consent management implementation.