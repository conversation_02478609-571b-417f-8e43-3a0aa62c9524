---
title: Consent Manager Dialog
description: An accessible, animated modal interface that wraps the Consent Manager Widget for a focused privacy customization experience.
---
import { LayersIcon, CookieIcon, ToggleRightIcon } from 'lucide-react';
import DialogExample from '~/examples/react/dialog';

The Consent Manager Dialog provides a clean, focused way for users to customize their privacy preferences. Think of it as a spotlight that dims the rest of your application while users make important privacy decisions. When users click "Customize" on your cookie banner, this dialog smoothly appears with detailed privacy controls.

<DialogExample  />

## How It Works

The dialog acts as a wrapper around the Consent Manager Widget, adding several key features:
1. A smooth fade-in animation that draws attention to privacy settings
2. An overlay that dims the rest of the application
3. Proper focus management for accessibility
4. Automatic portal rendering to avoid layout issues
5. Smart handling of mounting and unmounting

Let's explore how to implement this in your application.

## Quick Start

First, install the package if you haven't already:

{/* ```package-install
@c15t/react
``` */}

Then add the dialog to your application:

```tsx
import {
	ConsentManagerDialog,
	ConsentManagerProvider,
} from "@c15t/react";

function App() {
  return (
    <ConsentManagerProvider>
      <YourApp />
      <ConsentManagerDialog />
    </ConsentManagerProvider>
  )
}
```

The dialog will automatically:
- Mount itself correctly in the DOM
- Handle animations smoothly
- Manage focus when opened
- Maintain accessibility standards

## Understanding Animations

The dialog uses thoughtful animations to create a polished user experience. When opened, it:
1. Fades in an overlay to dim the background
2. Scales and fades in the dialog content
3. Smoothly reverses these animations when closing

You can control this behavior with the `disableAnimation` prop:

```tsx
// Disable animations if needed
<ConsentManagerDialog disableAnimation />
```

## Customizing Appearance

The dialog supports two main approaches to styling:

### Using Themes

Apply custom styles through the theme prop:

```tsx
<ConsentManagerDialog
  theme={{
    "widget.root": "",
	  "widget.branding": "",
	  "widget.footer": "",
	  "widget.footer.sub-group": "",
		"widget.footer.reject-button": "",
		"widget.footer.accept-button": "",
		"widget.footer.customize-button": "",
		"widget.footer.save-button": "",
		"widget.accordion": "",
		"widget.accordion.trigger": "",
		"widget.accordion.trigger-inner": "",
		"widget.accordion.item": "",
		"widget.accordion.icon": "",
		"widget.accordion.arrow.open": "",
		"widget.accordion.arrow.close": "",
		"widget.accordion.content": "",
		"widget.accordion.content-inner": "",
		"widget.switch": "",
		"widget.switch.track": "",
		"widget.switch.thumb": "",
		"widget.dialog": "",
		"widget.dialog.root": "",
		"widget.dialog.header": "",
		"widget.dialog.title": "",
		"widget.dialog.description": "",
		"widget.dialog.content": "",
		"widget.dialog.footer": "",
		"widget.overlay": "",
  }}
/>
```

### Removing Default Styles

For complete styling control, disable the default styles:

```tsx
<ConsentManagerDialog noStyle />
```

This gives you a blank canvas to build upon while maintaining the dialog's functionality.

## Integration Examples

### With Custom Trigger Button

```tsx
function PrivacyCenter() {
  const consentManager = useConsentManager()
  
  return (
    <button
      onClick={() => consentManager.setIsPrivacyDialogOpen(true)}
      className="privacy-button"
    >
      Privacy Settings
    </button>
  )
}
```

## Accessibility Features

The dialog implements several accessibility best practices:

### Focus Management
When the dialog opens, it:
1. Traps focus within the dialog
2. Sets initial focus on the first interactive element
3. Remembers and restores the previous focus position when closed

### Focus Trapping

The dialog implements focus trapping to ensure keyboard navigation remains within the dialog while it's open. This is crucial for:

- **Keyboard users**: Prevents users from accidentally interacting with content hidden behind the modal
- **Screen reader users**: Maintains proper context and prevents confusion
- **WCAG compliance**: Supports 2.4.3 Focus Order and provides proper modal functionality

#### How Focus Trapping Works

The `ConsentManagerDialog` uses the `useFocusTrap` hook internally to:

1. Capture the element that had focus before the dialog opened
2. Set initial focus to the first interactive element inside the dialog
3. Keep focus cycling within the dialog when users press Tab or Shift+Tab
4. Restore focus to the original element when the dialog closes

You can control focus trapping with the `trapFocus` prop:

```tsx
// Default behavior (recommended for accessibility)
<ConsentManagerDialog trapFocus={true} />

// Disable focus trapping (not recommended)
<ConsentManagerDialog trapFocus={false} />
```

<Callout type="info">
  Focus trapping is enabled by default and is recommended for WCAG compliance. Only disable it if you have a specific reason and are implementing alternative accessibility measures.
</Callout>

### Keyboard Navigation
Users can:
- Close the dialog with the Escape key
- Navigate controls with Tab
- Interact with all elements using only the keyboard

### Screen Readers
The dialog announces itself appropriately with:
- Proper ARIA roles and attributes
- Clear labeling of controls
- Status updates when opened/closed

## Technical Details

### Portal Rendering

The dialog uses React's createPortal to render outside your main application hierarchy. This ensures:
- No CSS specificity conflicts
- Proper stacking context
- Clean DOM structure

### Client-side Only

To prevent hydration issues, the dialog only renders on the client side. This is handled automatically through the `isMounted` state.

## API Reference

### Props

| Prop | Type | Description | Default |
|------|------|-------------|---------|
| theme | ConsentManagerWidgetTheme | Theme configuration | {} |
| disableAnimation | boolean | Turns off animations | false |
| noStyle | boolean | Removes default styles | false |

### Theme Properties

| Property | Purpose | Example Value |
|----------|---------|---------------|
| dialog | Main dialog container | "fixed inset-0" |
| overlay | Background overlay | "bg-black/50" |
| content | Dialog content wrapper | "bg-white p-4" |

## Best Practices

1. **Performance**
   - Only import the dialog where needed
   - Consider lazy loading if not immediately required
   - Use the disableAnimation prop on lower-end devices

2. **User Experience**
   - Keep the dialog focused on privacy settings
   - Provide clear save/cancel actions
   - Maintain consistent styling with your application

3. **Accessibility**
   - Test with keyboard navigation
   - Verify screen reader announcements
   - Ensure sufficient color contrast

## Related Components

<Cards>
  <Card 
    icon={<CookieIcon />}
    title="Cookie Banner"
    description="The friendly first-touch privacy notice that greets users. Customizable, accessible, and designed to get out of the way."
    href="/docs/components/react/cookie-banner"
  />
  <Card 
    icon={<ToggleRightIcon />}
    title="Consent Manager Widget"
    description="The engine that powers granular consent management. Easily embed detailed privacy controls anywhere in your app."
    href="/docs/components/react/consent-manager-widget"
  />
</Cards>
