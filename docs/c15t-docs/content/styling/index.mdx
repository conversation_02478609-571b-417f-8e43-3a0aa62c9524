---
title: General Styling
description: Learn the core concepts of the @c15t/react theming system and how it enables flexible component styling.
---

## Understanding the Theming System

Think of our theming system as a set of style instructions that cascade through your components, similar to CSS but with more structure and type safety. Each component has specific customizable areas, called theme keys, that you can target for styling.

The theming system provides a structured way to customize the appearance of components while maintaining their functionality and accessibility. It allows you to:

- Target specific elements within a component
- Apply consistent styling across your application
- Ensure type safety and prevent styling errors

## Theme Key Structure

Every theme key in our system follows a predictable pattern that makes it easy to target specific elements within a component:

```
'componentName.elementPath.subElement'
```

1. **Component Name**: The base name of the component (e.g., `banner`, `widget`, `dialog`)
2. **Element Path**: The path to the element within the component (e.g., `header.title`, `footer.accept-button`)
3. **State Variations**: Optional state indicators (e.g., `switch.thumb.checked`, `button.hover`)

### Component Hierarchies

Components are structured in a hierarchical manner, which is reflected in their theme keys:

```tsx
// Example of nested component styling
const theme = {
  'widget.root': 'container mx-auto',
  'widget.accordion': 'space-y-2',
  'widget.accordion.item': 'border rounded-sm',
  'widget.accordion.trigger': 'p-4 hover:bg-gray-50',
  'widget.accordion.trigger-inner': '',
  'widget.accordion.content': 'p-4 bg-gray-50'
}
```

Each level in the hierarchy can be styled independently, giving you fine-grained control over the appearance.

### Visualizing the Hierarchy

Here's a simplified visualization of a typical accordion component:

```
widget.root
└── widget.accordion
    └── widget.accordion.item
        ├── widget.accordion.trigger
        │   └── widget.accordion.trigger-inner
        └── widget.accordion.content
```

## Using Theme Context

For consistent styling across your application, you can use the `ThemeProvider` component to apply a global theme:

```tsx
import { ThemeProvider } from '@c15t/react'

const globalTheme = {
  'banner.root': 'bg-white rounded-lg shadow-lg',
  'dialog.root': 'bg-white rounded-lg shadow-xl',
  'widget.root': 'bg-gray-50 rounded border'
}

function App() {
  return (
    <ThemeProvider theme={globalTheme}>
      <YourApp />
    </ThemeProvider>
  )
}
```

### Theme Inheritance

Components can still define their own themes, which will merge with and override the global theme:

```tsx
<ThemeProvider theme={globalTheme}>
  {/* Uses the global theme */}
  <CookieBanner />
  
  {/* Overrides parts of the global theme */}
  <CookieBanner 
    theme={{
      'banner.root': 'bg-blue-50 p-4 rounded-lg'
    }}
  />
</ThemeProvider>
```

## Type-Safe Styling

Our theming system includes TypeScript support to help prevent errors:

```tsx
import type { CookieBannerTheme } from '@c15t/react'

// TypeScript will catch invalid theme keys
const theme: CookieBannerTheme = {
  'banner.root': 'bg-white p-4 rounded-lg shadow-lg',
  'banner.invalid': 'TypeScript error!' // Error: invalid key
}
```

## Disabling Default Styles

Sometimes you might want to start from scratch. Use the `noStyle` prop to remove all default styling:

```tsx
<CookieBanner noStyle theme={yourCustomTheme} />
```

## Best Practices

1. **Maintain Accessibility**
   - Ensure sufficient color contrast
   - Keep interactive elements visually distinct
   - Test your styles with screen readers

2. **Consider Responsive Design**
   - Start with mobile-first styles
   - Use flexible units (rem, em) over fixed pixels
   - Test across different screen sizes

3. **Performance**
   - Group related styles together
   - Avoid unnecessary style overrides
   - Use CSS classes over inline styles when possible

4. **Maintainability**
   - Keep theme configurations organized
   - Document custom styles
   - Use consistent naming conventions 