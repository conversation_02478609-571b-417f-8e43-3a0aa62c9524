---
title: JavaScript Quickstart
description: Learn how to integrate c15t into your Javacript application with this step-by-step guide. We'll cover installation, configuration, and basic usage.
---

## CLI Setup (Recommended)
<Steps>
<Step>
### Generate Schema + Code

<RunCommand command="@c15t/cli generate" />

</Step>

<Step>
### Run Database Migrations (Optional)

<Callout type="note">
This is only required if you are self hosting c15t.
</Callout>

<RunCommand command="@c15t/cli migrate" />

</Step>
</Steps>

## Manual Setup
<Steps>
<Step>
### Install `c15t` Package
 ```package-install
 c15t
 ```
</Step>
 
<Step>
 
### Add to Your JavaScript Application

```tsx
import { configureConsentManager, type ConsentManagerOptions } from 'c15t';

export const c15tConfig = {
	mode: 'offline',

	// Optional: Add callback functions for various events
	callbacks: {
		onConsentSet: (response: any) => {
			console.log('Consent has been saved locally');
		},
	},
} satisfies ConsentManagerOptions;

export const consentManager = configureConsentManager(c15tConfig);

```

The consent manager is now ready to use. For example: 

```tsx
consentManager.setConsent({
  body: {
    type: 'cookie_banner',
    domain: 'localhost',
    preferences: {
      necessary: true,
      marketing: true,
    },
  },
});
```

<Callout type="tip">
You can create an instance at [consent.io](https://consent.io) (recommended) or use c15t offline by setting `mode: 'offline'`.
</Callout>
</Step>
</Steps>

--- 

## Hosting Options

### Creating a consent.io Instance (Recommended)

<Callout type="info">
Using consent.io is the recommended method as it is the easiest way to get started and requires little maintenance.
</Callout>

[consent.io](https://consent.io) provides a fully managed consent management service. This is the recommended method as it is the easiest way to get started and requires little maintenance.

<Steps>

<Step>
Sign up for a [consent.io](https://consent.io) account.
</Step>

<Step>
After signing up, create a new instance, located in the top-right corner.

<Callout>
When creating an instance it is important to list all the trusted origins for your application such as "localhost", your production domain, etc.
</Callout>

</Step>

<Step>
After the instance is created, you will be given a backendURL, which you can add to your `ConsentManagerOptions`.

A backend URL might look like this: `https://<my-instance>.c15t.dev/`.
</Step>

</Steps>

### Alternative Storage Options

<Callout type="note">
For more advanced setup options, choose the approach that best suits your requirements.
</Callout>

For more advanced setup options, please refer to:
- [Overview](/docs/storing-consent/overview) - Compare different approaches to storing consent decisions in your application
- [Hosted c15t](/docs/storing-consent/hosted-c15t) - Complete guide to using consent.io
- [Offline Mode](/docs/storing-consent/offline-mode) - Complete guide to using c15t without a backend
- [Custom Client](/docs/storing-consent/custom-client) - Advanced implementation with custom handlers for full control

## Decision Guide

<Callout>
Use this flowchart to determine which c15t configuration is best for your needs.
</Callout>

<Mermaid chart={`
flowchart TD
    Start([Start here]) --> StoreConsent
    
    StoreConsent{Need to store\nconsent choices?}
    StoreConsent -->|Yes| ManagedService
    StoreConsent -->|No| OfflineMode
    
    ManagedService{Want a managed\nservice?}
    ManagedService -->|Yes| ConsentIO
    ManagedService -->|No| CustomClient
    
    OfflineMode([c15t Offline Mode]):::optionStyle
    OfflineMode -.-> OfflineNote[Client-side only\nStores in localStorage]:::noteStyle
    
    ConsentIO([consent.io]):::recommendStyle
    ConsentIO -.-> ConsentIONote[Fully managed\nSimplest setup]:::noteStyle
    
    CustomClient([Custom Client]):::optionStyle
    CustomClient -.-> CustomNote[Full control\nRequires implementation]:::noteStyle
    
    %% Styling
    classDef recommendStyle fill:#4caf50,stroke:#388e3c,color:white,stroke-width:2px;
    classDef optionStyle fill:#37474f,stroke:#263238,color:white,stroke-width:1px;
    classDef noteStyle fill:#424242,stroke:none,color:#aaa,font-size:12px;
`} />

## Next Steps

<Callout type="tip">
Choose your next step based on your specific implementation needs.
</Callout>

<div className="mt-6">
  <CompactCard 
    href="/docs/javascript/storing-consent/hosted-c15t" 
    icon={
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    }
  >
    Learn more about <code>Hosted c15t (consent.io)</code>
  </CompactCard>

  <CompactCard 
    href="/docs/javascript/storing-consent/self-hosting"
    icon={
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2" />
      </svg>
    }
  >
    Learn more about <code>Self-Hosting</code>
  </CompactCard>

  <CompactCard 
    href="/docs/javascript/storing-consent/offline-mode"
    icon={
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636a9 9 0 010 12.728m0 0l-2.829-2.829m2.829 2.829L21 21M15.536 8.464a5 5 0 010 7.072m0 0l-2.829-2.829m-4.243 2.829a4.978 4.978 0 01-1.414-2.83m-1.414 5.658a9 9 0 01-2.167-9.238m7.824 2.167a1 1 0 111.414 1.414m-1.414-1.414L10 10" />
      </svg>
    }
  >
    Learn more about <code>Offline Mode</code>
  </CompactCard>
</div>

