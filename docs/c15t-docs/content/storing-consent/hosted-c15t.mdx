---
title: 'Hosted Solution'
description: 'Use consent.io for an easy, managed consent storage solution with minimal setup'
---
import { Ri<PERSON><PERSON>lightLine, RiTimerLine, RiServerLine, RiSettings4Line } from '@remixicon/react';

<Callout type="default">
  <h3>Congratulations, you've chosen the best way to deploy to production!</h3>
  
  consent.io provides a fully managed service that removes all the infrastructure complexity. You can focus on your application while we handle scaling, updates, and compliance monitoring. For organizations with specific requirements, we also offer [self-hosting](/docs/storing-consent/self-hosting) options.
</Callout>

[consent.io](https://consent.io) provides a fully managed consent management service, offering the easiest way to implement robust server-based consent storage with minimal setup and maintenance.

## Key Benefits

- **Zero Backend Maintenance** - No server setup or management required
- **Automatic Updates** - Always running the latest version
- **Built-in Scaling** - Handles traffic spikes without configuration
- **Geographic Detection** - Built-in jurisdiction detection for compliance
- **Analytics Dashboard** - Insights into consent patterns and compliance
- **Cross-Device Sync** - Users maintain consistent consent status across all devices

## Getting Started

<Steps>
<Step>
### Create a consent.io Instance

1. Sign up for a [consent.io](https://consent.io) account
2. Create a new instance in the dashboard
3. Configure your trusted origins (domains that can connect to your instance)
4. Copy the provided backendURL (e.g., `https://your-instance.c15t.dev`)
</Step>

<Step>
### Configure Your Client

Set up your frontend to use the consent.io instance:

```tsx
// app/layout.tsx
import { 
  ConsentManagerDialog,
  ConsentManagerProvider,
  CookieBanner
} from '@c15t/react';

export default function RootLayout({ children }) {
  const options = {
    mode: 'c15t',
    backendURL: 'https://your-instance.c15t.dev',
  };

  return (
    <ConsentManagerProvider options={options}>
      {children}
      <CookieBanner />
      <ConsentManagerDialog />
    </ConsentManagerProvider>
  );
}
```
</Step>

<Step>
### Test Your Integration

1. Run your application
2. Verify the consent banner appears 
3. Check the consent.io dashboard to confirm data is being recorded
</Step>
</Steps>

## Client Configuration

Configure your frontend to connect to consent.io:

```tsx
const options = {
  // Required: Set to 'c15t' mode for hosted services
  mode: 'c15t',

  // Required: URL to your consent.io instance
  backendURL: 'https://your-instance.c15t.dev',
  
  
  // Optional: Global event callbacks
  callbacks: {
    onError: (response, path) => {
      console.error(`Request to ${path} failed:`, response.error);
    },
    onConsentSet: (response) => {
      console.log('Consent set successfully');
    }
  }
};
```

<CompactCard 
  href="/docs/nextjs/quickstart" 
  icon={<RiFlashlightLine size={18} />}
>
  See the <code>Next.js Quickstart Guide</code> for more frontend integration details
</CompactCard>

## Alternative: Self-Hosting

If you need complete control over your consent management infrastructure, you can self-host a c15t instance instead of using consent.io.

<Callout type="warning">
Self-hosting requires more setup and maintenance effort but gives you complete control over your data and infrastructure.
</Callout>

<CompactCard 
  href="/docs/storing-consent/self-hosting" 
  icon={<RiServerLine size={18} />}
>
  See the <code>Self-Hosting Guide</code> for detailed setup instructions
</CompactCard>

## Deployment Options

### Docker Support

<Callout type="warning">
Docker support for c15t is not currently available out-of-the-box. We're interested in adding this feature and would welcome community contributions.
</Callout>

If you're interested in containerizing c15t for Docker deployments:

- We don't currently provide official Docker images or Dockerfiles
- This is on our roadmap but doesn't have a specific timeline
- Community contributions for Docker support would be greatly appreciated
- You can propose Docker implementations through our [GitHub issue](https://github.com/c15t/c15t/issues/162)

Until official Docker support is available, we recommend using one of our existing deployment options:

1. **consent.io (hosted)** - Zero setup, fully managed service
2. **Self-hosting with Next.js API routes** - Integrated into your existing application
3. **Standalone server** - Standard Node.js deployment

## When to Use Hosted c15t

Choose consent.io (hosted c15t) when:

- You want the simplest implementation with minimal backend configuration
- You prefer not to manage database setup and maintenance
- You need geographic jurisdiction detection built-in
- You want automatic updates and scaling

Consider [Offline Mode](/nextjs/storing-consent/offline-mode) for simple applications without cross-device needs, or [Self-Hosting](/nextjs/storing-consent/self-hosting) for complete control.

## Next Steps

<div className="mt-6">
  <CompactCard 
    href="/docs/storing-consent/offline-mode" 
    icon={<RiTimerLine size={18} />}
  >
    Configure <code>Offline Mode</code>
  </CompactCard>

  <CompactCard 
    href="/docs/storing-consent/self-hosting" 
    icon={<RiServerLine size={18} />}
  >
    Explore <code>Self-Hosting</code> options
  </CompactCard>

  <CompactCard 
    href="/docs/storing-consent/custom-client" 
    icon={<RiSettings4Line size={18} />}
  >
    Create a <code>Custom Client</code>
  </CompactCard>
</div>