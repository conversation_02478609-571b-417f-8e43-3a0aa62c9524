---
title: 'Offline Mode'
description: 'Store consent decisions in the browser with offline mode, perfect for sites without backend requirements'
---
import { RiServerLine, RiGlobalLine, RiSettings4Line } from '@remixicon/react';

The offline mode provides a simple, browser-based approach to storing user consent decisions without requiring a backend server.

## Key Characteristics

- **No backend required** - Everything is stored locally in the browser
- **Simplified setup** - Get started quickly with minimal configuration
- **Independence** - Works without external services or APIs
- **Fast implementation** - Ideal for prototyping and simpler sites

## Implementation

<Steps>
<Step>
### Install the Package

```package-install
@c15t/react
```
</Step>

<Step>
### Configure the Client

Set up the c15t client with offline mode:

```jsx
// lib/c15tClient.js
import { createClient } from '@c15t/react'

export const c15tClient = createClient({
  mode: 'offline',
})
```
</Step>

<Step>
### Add the Provider

Wrap your application with the ConsentManagerProvider:

```jsx
import { ConsentManagerProvider } from '@c15t/react'
import { c15tClient } from '../lib/c15tClient'

function MyApp({ Component, pageProps }) {
  return (
    <ConsentManagerProvider options={c15tClient}>
      <Component {...pageProps} />
    </ConsentManagerProvider>
  )
}

export default MyApp
```
</Step>
</Steps>

## How It Works

<Callout type="note">
Offline mode provides the same API interface as the standard client but operates completely client-side.
</Callout>

The offline mode implements the same interface as the standard client, but with the following differences:

1. **Storage**: All consent preferences are stored in the browser's localStorage using the configured key
2. **Network**: No network requests are made, all operations happen locally
3. **Consent Banner**: The banner visibility is determined by checking if a value exists in localStorage
4. **Consent Verification**: Always returns a successful response

## Configuration Options

The offline mode accepts the following configuration options:

```jsx
const options = {
  mode: 'offline',
  // Optional: Add callback functions for various events
  callbacks: {
    onConsentBannerFetched: (response) => {
      console.log('Banner state retrieved:', response.data);
    },
    onConsentSet: (response) => {
      console.log('Consent preferences saved');
    },
    onConsentVerified: (response) => {
      console.log('Consent verification complete');
    },
    onError: (error, endpoint) => {
      console.error(`Error in ${endpoint}:`, error);
    }
  }
};
```

## Storage Mechanisms

In offline mode, consent decisions are stored in the browser using:

### LocalStorage

By default, c15t uses the browser's localStorage to persist consent decisions:

```jsx
// Default implementation
export const c15tClient = createClient({
  mode: 'offline',
  storage: 'localStorage' // This is the default, so can be omitted
})
```

### SessionStorage

For session-based consent that's cleared when the browser is closed:

```jsx
export const c15tClient = createClient({
  mode: 'offline',
  storage: 'sessionStorage'
})
```

### Memory Only

For applications where persistence isn't needed:

```jsx
export const c15tClient = createClient({
  mode: 'offline',
  storage: 'memory'
})
```

## Browser Compatibility

<Callout type="warning">
Some browser environments like private browsing modes may have localStorage restrictions.
</Callout>

The offline mode relies on localStorage, which is supported in all modern browsers. However, it includes fallbacks for environments where localStorage might be unavailable or restricted:

- Private browsing modes in some browsers
- Cookie-blocking browser extensions
- Browsers with storage permissions disabled

In these cases, the client will log a warning and continue functioning with defaults.

## Use Cases

### Development and Testing

Offline mode is perfect for development and testing environments where you don't want to set up a backend:

```jsx
// Development configuration
const options = {
  mode: process.env.NODE_ENV === 'development' 
    ? 'offline' 
    : 'c15t',
  backendURL: process.env.NODE_ENV === 'production'
    ? process.env.NEXT_PUBLIC_C15T_URL
    : undefined
};
```

### Static Sites

<Callout type="tip">
Offline mode is an excellent choice for static sites deployed on platforms like Vercel, Netlify, or GitHub Pages.
</Callout>

For static sites without backend integration, offline mode provides a simple solution:

```jsx
const options = {
  mode: 'offline',
};
```

### Fallback Mode

You can use offline mode as a fallback when the backend is unavailable:

```jsx
import { useState, useEffect } from 'react';

function ConsentProvider({ children }) {
  const [mode, setMode] = useState('c15t');
  
  useEffect(() => {
    // Check if backend is available
    fetch('/api/c15t/status')
      .catch(() => {
        console.warn('c15t backend unavailable, switching to offline mode');
        setMode('offline');
      });
  }, []);
  
  const options = {
    mode,
    backendURL: mode === 'c15t' ? '/api/c15t' : undefined
  };
  
  return (
    <ConsentManagerProvider options={options}>
      {children}
    </ConsentManagerProvider>
  );
}
```

## Limitations

<Callout type="important">
Understand these limitations when deciding if offline mode is right for your application.
</Callout>

While offline mode provides a functional consent management solution, it has some limitations:

1. **No Centralized Reporting**: Since all data is stored locally, you can't generate reports or analytics
2. **Device-Specific**: Consent preferences don't transfer between devices or browsers
3. **Storage Limits**: localStorage has size limitations (typically 5-10MB per domain)
4. **No Server-Side Logic**: Custom server-side processing of consent isn't possible

## When to Use Offline Mode

Consider using offline mode when:

- You're building a prototype or MVP
- Your site doesn't have a backend
- You want the simplest possible implementation
- Cross-device synchronization isn't a requirement
- You have limited compliance needs

For more complex applications or those with stricter compliance requirements, consider [Hosted c15t](/docs/storing-consent/hosted-c15t) instead.

## Next Steps

<div className="mt-6">
  <CompactCard 
    href="/docs/storing-consent/self-hosting" 
    icon={<RiServerLine size={18} />}
  >
    Configure <code>Self-Hosting</code>
  </CompactCard>

  <CompactCard 
    href="/docs/storing-consent/hosted-c15t" 
    icon={<RiGlobalLine size={18} />}
  >
    Use <code>Hosted c15t</code>
  </CompactCard>

  <CompactCard 
    href="/docs/storing-consent/custom-client" 
    icon={<RiSettings4Line size={18} />}
  >
    Create a <code>Custom Client</code>
  </CompactCard>
</div> 