---
title: Building Privacy Tools in the Open
description: We believe great developer tools should be built in the open, with transparency and community collaboration at their core. This philosophy guides how we're building modern privacy infrastructure.
---

## Open Source Foundation

Consent Management (c15t) is built with a strong open source foundation:

- **Core Platform**: GNU3 licensed, ensuring your privacy tools remain free and open
- **UI Components**: Built on shadcn/ui, giving you complete customization control
- **Server Components**: Full Next.js app directory support for modern applications
- **Cloud Platform**: Optional managed service coming soon (while keeping core open source)

## Why We Chose Open Source

Privacy infrastructure should be:

1. **Transparent**
   - See exactly how user consent is managed
   - Audit the code handling sensitive data
   - Understand the complete data flow

2. **Community-Driven**
   - Benefit from collective expertise
   - Shape the future of privacy tools
   - Share best practices globally

3. **Trustworthy**
   - No black boxes in privacy management
   - Full visibility into data handling
   - Community-verified security

4. **Flexible**
   - Self-host for complete control
   - Customize to your exact needs
   - Integrate with your existing stack

## Following Giants

We're inspired by transformative developer platforms:

- **Vercel**: Revolutionized deployment workflows
- **Clerk**: Simplified authentication
- **Resend**: Modernized email infrastructure
- **Unkey**: Streamlined API key management

Just as these platforms brought developer experience to the forefront, we're bringing that same level of excellence and simplicity to privacy management.

## Our Commitment

By choosing open source, we commit to:

- **Transparency**: All core code is public and auditable
- **Community**: Decisions made with community input
- **Quality**: Enterprise-grade while remaining open
- **Longevity**: Sustainable open source development

## Get Started

Join us in building the future of privacy management:

<Cards>
  <Card 
    title="Contributing to c15t.com" 
    description="Learn how to contribute and join our community"
    href="/docs/open-source/contributing" 
  />
  <Card 
    title="License" 
    description="Understanding the GNU3 license and your rights"
    href="/docs/open-source/license" 
  />
</Cards>