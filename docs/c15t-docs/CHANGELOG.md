# docs

## 1.3.7

### Patch Changes

- Updated dependencies [c463eda]
  - @c15t/react@1.4.3
  - @c15t/nextjs@1.4.3

## 1.3.7-canary-20250708133115

### Patch Changes

- Updated dependencies [357dcd7]
  - @c15t/nextjs@1.4.3-canary-20250708133115
  - @c15t/react@1.4.3-canary-20250708133115

## 1.3.6

### Patch Changes

- Updated dependencies [53774ce]
- Updated dependencies [53774ce]
- Updated dependencies [53774ce]
  - @c15t/nextjs@1.4.2
  - @c15t/react@1.4.2
  - c15t@1.4.2
  - @c15t/dev-tools@1.4.2
  - @c15t/backend@1.4.2

## 1.3.6-canary-20250702103734

### Patch Changes

- Updated dependencies [b2c7c0f]
- Updated dependencies [797b0f7]
- Updated dependencies [eed347c]
  - @c15t/nextjs@1.4.2-canary-20250702103734
  - @c15t/react@1.4.2-canary-20250702103734
  - c15t@1.4.2-canary-20250702103734
  - @c15t/dev-tools@1.4.2-canary-20250702103734
  - @c15t/backend@1.4.2-canary-20250702103734

## 1.3.5

### Patch Changes

- Updated dependencies [34d2a46]
- Updated dependencies [8afd304]
  - @c15t/nextjs@1.4.1
  - @c15t/backend@1.4.1
  - c15t@1.4.1
  - @c15t/react@1.4.1
  - @c15t/dev-tools@1.4.1

## 1.3.4

### Patch Changes

- Updated dependencies [6eb9a8d]
  - c15t@1.4.0
  - @c15t/nextjs@1.4.0
  - @c15t/react@1.4.0
  - @c15t/dev-tools@1.4.0

## 1.3.3

### Patch Changes

- Updated dependencies [b4d53be]
  - c15t@1.3.3
  - @c15t/nextjs@1.3.3
  - @c15t/react@1.3.3
  - @c15t/dev-tools@1.3.3

## 1.3.3-canary-20250624131627

### Patch Changes

- Updated dependencies [15d7a9b]
- Updated dependencies [63200df]
- Updated dependencies [f13ad52]
  - @c15t/nextjs@1.3.3-canary-20250624131627
  - c15t@1.3.3-canary-20250624131627
  - @c15t/react@1.3.3-canary-20250624131627
  - @c15t/dev-tools@1.3.3-canary-20250624131627

## 1.3.2

### Patch Changes

- Updated dependencies [31fafe7]
  - @c15t/nextjs@1.3.2

## 1.3.2-canary-20250623195533

### Patch Changes

- Updated dependencies [039576e]
  - @c15t/nextjs@1.3.2-canary-20250623195533

## 1.3.1

### Patch Changes

- 7fecb81: refactor(nextjs): fetch inital data from backend in c15t mode instead of duplicate logic
  fix: incorrect link to quickstart
  fix(issue-274): include nextjs externals in rslib
  fix(core): fall back to API call if initialData promise is empty
  chore: add translation for zh
- Updated dependencies [7fecb81]
  - @c15t/dev-tools@1.3.1
  - @c15t/backend@1.3.1
  - @c15t/nextjs@1.3.1
  - @c15t/react@1.3.1
  - c15t@1.3.1

## 1.3.1-canary-20250622133205

### Patch Changes

- Updated dependencies [5c4cd75]
- Updated dependencies [e0b2597]
  - @c15t/nextjs@1.3.1-canary-20250622133205

## 1.3.1-canary-20250618084038

### Patch Changes

- Updated dependencies [95edb35]
  - c15t@1.3.1-canary-20250618084038
  - @c15t/backend@1.3.1-canary-20250618084038
  - @c15t/nextjs@1.3.1-canary-20250618084038
  - @c15t/dev-tools@1.3.1-canary-20250618084038
  - @c15t/react@1.3.1-canary-20250618084038

## 1.3.0

### Patch Changes

- Updated dependencies [85e5e3d]
  - @c15t/backend@1.3.0
  - c15t@1.3.0
  - @c15t/dev-tools@1.3.0
  - @c15t/nextjs@1.3.0
  - @c15t/react@1.3.0

## 1.1.8-canary-20250603153501

### Patch Changes

- Updated dependencies [e50e925]
  - @c15t/nextjs@1.2.2-canary-20250603153501
  - @c15t/react@1.2.2-canary-20250603153501
  - c15t@1.2.2-canary-20250603153501
  - @c15t/dev-tools@1.2.2-canary-20250603153501

## 1.1.8-canary-20250602152741

### Patch Changes

- Updated dependencies [131a2ff]
  - @c15t/backend@1.2.2-canary-20250602152741
  - c15t@1.2.2-canary-20250602152741
  - @c15t/nextjs@1.2.2-canary-20250602152741
  - @c15t/react@1.2.2-canary-20250602152741
  - @c15t/dev-tools@1.2.2-canary-20250602152741

## 1.1.8-canary-20250521150034

### Patch Changes

- Updated dependencies [8c2a0f4]
  - @c15t/nextjs@1.2.2-canary-20250521150034

## 1.1.8-canary-20250521133509

### Patch Changes

- Updated dependencies [e4b9778]
  - @c15t/backend@1.2.2-canary-20250521133509
  - c15t@1.2.2-canary-20250521133509
  - @c15t/nextjs@1.2.2-canary-20250521133509
  - @c15t/react@1.2.2-canary-20250521133509
  - @c15t/dev-tools@1.2.2-canary-20250521133509

## 1.1.8-canary-20250520100232

### Patch Changes

- Updated dependencies [eeda731]
  - @c15t/nextjs@1.2.2-canary-20250520100232

## 1.1.8-canary-20250514203718

### Patch Changes

- Updated dependencies [f24f11b]
  - @c15t/backend@1.2.2-canary-20250514203718
  - c15t@1.2.2-canary-20250514203718
  - @c15t/dev-tools@1.2.2-canary-20250514203718
  - @c15t/nextjs@1.2.2-canary-20250514203718
  - @c15t/react@1.2.2-canary-20250514203718

## 1.1.8-canary-20250514183211

### Patch Changes

- Updated dependencies [f64f000]
  - @c15t/backend@1.2.2-canary-20250514183211
  - @c15t/nextjs@1.2.2-canary-20250514183211
  - @c15t/react@1.2.2-canary-20250514183211
  - c15t@1.2.2-canary-20250514183211
  - @c15t/dev-tools@1.2.2-canary-20250514183211

## 1.1.7

### Patch Changes

- Updated dependencies [[`aca32d3`](https://github.com/c15t/c15t/commit/aca32d3f0f76d75ad618a8ba3386ce385ac612e4)]:
  - @c15t/backend@1.2.1
  - c15t@1.2.1
  - @c15t/react@1.2.1
  - @c15t/dev-tools@1.2.1

## 1.1.6

### Patch Changes

- Updated dependencies [[`838a9b5`](https://github.com/c15t/c15t/commit/838a9b52c31326899ec3c903e43bf7bc31a6490f), [`b1de2ba`](https://github.com/c15t/c15t/commit/b1de2baccd63295d49fb2868f63659f5ff48a9ce)]:
  - @c15t/backend@1.2.0
  - c15t@1.2.0
  - @c15t/react@1.2.0
  - @c15t/dev-tools@1.2.0

## 1.1.4

### Patch Changes

- Updated dependencies [[`2d81c9f`](https://github.com/c15t/c15t/commit/2d81c9fc84ee960e46196dfd460407a925901a82)]:
  - c15t@1.1.4
  - @c15t/react@1.1.4
  - @c15t/dev-tools@1.1.4

## 1.1.3

### Patch Changes

- [#203](https://github.com/c15t/c15t/pull/203) [`4d47e21`](https://github.com/c15t/c15t/commit/4d47e2109bfc894f1666b19f4ff40d7398f10c57) Thanks [@KayleeWilliams](https://github.com/KayleeWilliams)! - fix(core): callbacks not working on c15t mode

- Updated dependencies [[`e6a6765`](https://github.com/c15t/c15t/commit/e6a6765a9466d18d3b17e2f08151a63a655442a7), [`4d47e21`](https://github.com/c15t/c15t/commit/4d47e2109bfc894f1666b19f4ff40d7398f10c57)]:
  - @c15t/react@1.1.3
  - c15t@1.1.3
  - @c15t/dev-tools@1.1.3

## 0.0.1

### Patch Changes

- Updated dependencies [[`08446ae`](https://github.com/c15t/c15t/commit/08446aef443a20a2262477a1dca3569d6bf672ad)]:
  - c15t@1.0.1
  - @c15t/dev-tools@1.0.1
  - @c15t/react@1.0.1
