import type { SVGProps } from "react";

export const ReactIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 300 300"
    {...props}
  >
    <title>React</title>
    <path
      fill="currentColor"
      d="M176.789 149.759c0-14.344-11.993-25.974-26.789-25.974-14.794 0-26.789 11.63-26.789 25.974 0 14.346 11.995 25.975 26.789 25.975 14.796 0 26.789-11.629 26.789-25.975Z"
    />
    <path
      fill="currentColor"
      fillRule="evenodd"
      d="M237.004 101.537c5.657-22.29 12.768-63.622-12.3-77.635-24.951-13.952-57.42 12.782-74.565 28.83-17.095-15.888-50.476-42.508-75.523-28.45-24.94 13.998-17.25 54.646-11.467 77.075C39.883 107.764 0 121.564 0 149.759c0 28.115 39.842 43.129 62.97 49.53-5.804 22.553-13.11 62.62 11.863 76.588 25.14 14.055 58.417-11.932 75.721-28.155 17.255 16.142 49.412 42.299 74.368 28.292 25.034-14.051 18.514-54.83 12.731-77.389C260.073 192.21 300 177.523 300 149.759c0-27.924-40.097-41.836-62.996-48.222Zm-2.842 85.133c-3.797-11.654-8.922-24.047-15.191-36.849 5.984-12.5 10.91-24.733 14.601-36.312 16.787 4.719 53.613 15.504 53.613 36.25 0 20.941-35.339 31.828-53.023 36.911ZM218.5 265.26c-18.62 10.45-46.279-14.564-59.022-26.451 8.454-8.965 16.902-19.387 25.147-30.96 14.504-1.247 28.205-3.287 40.631-6.072 4.07 15.957 11.946 52.986-6.756 63.483Zm-137.264-.149c-18.622-10.411-10.11-46.165-5.84-62.778 12.287 2.636 25.89 4.532 40.428 5.675 8.299 11.324 16.992 21.733 25.752 30.849-10.823 10.151-41.631 36.713-60.34 26.254ZM12.815 149.759c0-21.019 36.606-31.726 53.775-36.433 3.755 11.839 8.68 24.218 14.622 36.736-6.02 12.704-11.014 25.279-14.803 37.258-16.372-4.544-53.594-16.529-53.594-37.561ZM81.037 35.035c18.698-10.493 47.71 15.063 60.122 26.566-8.715 9.074-17.328 19.405-25.556 30.664-14.109 1.268-27.615 3.304-40.032 6.032-4.659-18.09-13.212-52.78 5.466-63.262Zm112.756 70.736c9.572 1.171 18.743 2.729 27.354 4.635-2.585 8.034-5.807 16.435-9.602 25.047-5.501-10.12-11.406-20.037-17.752-29.682Zm-43.65-35.29c5.911 6.209 11.831 13.142 17.655 20.664a388.968 388.968 0 0 0-35.458-.008c5.829-7.452 11.8-14.375 17.803-20.656Zm-61.578 64.963c-3.732-8.582-6.925-17.02-9.533-25.169 8.56-1.857 17.688-3.375 27.198-4.527a363.032 363.032 0 0 0-17.665 29.696Zm17.938 59.213c-9.824-1.063-19.09-2.504-27.647-4.31 2.648-8.292 5.912-16.912 9.724-25.679a363.056 363.056 0 0 0 17.923 29.989Zm43.995 35.258c-6.074-6.355-12.133-13.384-18.05-20.944 11.88.452 23.783.453 35.662-.061-5.842 7.698-11.741 14.737-17.612 21.005Zm61.169-65.693c4.01 8.862 7.39 17.435 10.075 25.587-8.699 1.925-18.09 3.475-27.985 4.626 6.393-9.828 12.392-19.899 17.91-30.213Zm-34.194 31.719c-18.172 1.259-36.457 1.245-54.635.103a349.232 349.232 0 0 1-27.461-45.986 349.08 349.08 0 0 1 27.307-45.9 372.726 372.726 0 0 1 54.685.008c10.192 14.605 19.306 29.92 27.388 45.715-7.986 15.931-17.162 31.317-27.284 46.06Zm40.83-161.275c18.703 10.46 10.378 47.607 6.292 63.76-12.446-2.785-25.962-4.857-40.112-6.145-8.244-11.38-16.785-21.73-25.359-30.686 12.571-11.75 40.657-37.286 59.179-26.929Z"
      clipRule="evenodd"
    />
  </svg>
);

export const NextIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 300 300"
    {...props}
  >
    <title>Next.js</title>
    <defs>
      <path
        id="reuse-0"
        fill="#000"
        d="M150 300c82.843 0 150-67.157 150-150S232.843 0 150 0 0 67.157 0 150s67.157 150 150 150Z"
      />
    </defs>
    <g clipPath="url(#a)">
      <mask
        id="b"
        width={300}
        height={300}
        x={0}
        y={0}
        maskUnits="userSpaceOnUse"
        style={{
          maskType: "alpha",
        }}
      >
        <use href="#reuse-0" />
      </mask>
      <g mask="url(#b)">
        <use href="#reuse-0" />
        <path
          fill="url(#c)"
          d="M249.18 262.533 115.237 90H90v119.95h20.189v-94.311l123.143 159.103c5.556-3.719 10.85-7.8 15.848-12.209Z"
        />
        <path fill="url(#d)" d="M211.666 90h-20v120h20V90Z" />
      </g>
    </g>
    <defs>
      <linearGradient
        id="c"
        x1={181.667}
        x2={240.833}
        y1={194.167}
        y2={267.5}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#fff" />
        <stop offset={1} stopColor="#fff" stopOpacity={0} />
      </linearGradient>
      <linearGradient
        id="d"
        x1={201.666}
        x2={201.331}
        y1={90}
        y2={178.125}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#fff" />
        <stop offset={1} stopColor="#fff" stopOpacity={0} />
      </linearGradient>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h300v300H0z" />
      </clipPath>
    </defs>
  </svg>
);

export const HonoIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 300 300"
    {...props}
  >
    <title>Hono</title>
    <g clipPath="url(#a)">
      <path
        fill="#E36002"
        d="M155.562.025a569.102 569.102 0 0 0-65.65 101.825 107.397 107.397 0 0 1-6.937-6.625A347.446 347.446 0 0 0 67.912 75.95a109.524 109.524 0 0 0-15.637 26.5 259.297 259.297 0 0 0-18.1 73.5A110.838 110.838 0 0 0 38.4 219.3c16.4 43.5 47.425 69.913 93.062 79.213 38.188 5.475 71.938-4.163 101.213-28.9 33.462-32.375 41.987-70.425 25.587-114.15a416.02 416.02 0 0 0-37.35-69.875A1141.606 1141.606 0 0 0 157.687.663a2.696 2.696 0 0 0-2.125-.638Zm-4.2 48.825a636.512 636.512 0 0 1 59.925 81.9 169.704 169.704 0 0 1 13.85 30.125c7.575 29.825-.55 54.425-24.387 73.8-23.063 16.225-48.375 21.038-75.9 14.45-29.7-9.212-46.875-29.187-51.513-59.925a63.837 63.837 0 0 1 3.025-28.325 204.993 204.993 0 0 1 16.25-32.512l18.075-26.513a4960.823 4960.823 0 0 0 40.675-53Z"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h300v300H0z" />
      </clipPath>
    </defs>
  </svg>
);

export const JSIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 300 300"
    {...props}
  >
    <title>JavaScript</title>
    <g clipPath="url(#a)">
      <path
        fill="#F7DF1C"
        d="M0 0v300h300V0H0Zm163.259 233.973c0 29.197-17.143 42.523-42.121 42.523-22.567 0-35.625-11.652-42.321-25.782l22.969-13.861c4.419 7.835 8.437 14.464 18.147 14.464 9.241 0 15.134-3.616 15.134-17.746v-95.825h28.192v96.227Zm66.696 42.523c-26.183 0-43.125-12.456-51.361-28.795l22.969-13.259c6.026 9.844 13.928 17.143 27.79 17.143 11.651 0 19.151-5.826 19.151-13.929 0-9.643-7.633-13.058-20.558-18.75l-7.031-3.013c-20.357-8.639-33.817-19.554-33.817-42.522 0-21.161 16.139-37.233 41.25-37.233 17.947 0 30.804 6.228 40.045 22.567l-21.964 14.063c-4.822-8.639-10.045-12.054-18.148-12.054-8.236 0-13.46 5.224-13.46 12.054 0 8.437 5.224 11.853 17.344 17.143l7.031 3.013c23.974 10.246 37.433 20.759 37.433 44.33 0 25.313-19.955 39.242-46.674 39.242Z"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M0 0h300v300H0z" />
      </clipPath>
    </defs>
  </svg>
);

export const iconMap = {
  react: ReactIcon,
  next: NextIcon,
  hono: HonoIcon,
  js: JSIcon,
};
