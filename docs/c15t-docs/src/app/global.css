@import "tailwindcss";
@import "fumadocs-ui/css/black.css";
@import "fumadocs-ui/css/preset.css";

@source '../../node_modules/fumadocs-ui/dist/**/*.js';

:root {
  --color-fd-primary: hsl(172 72.2% 48%);
  --fd-layout-width: 1400px !important;
}

:root .dark {
  --color-fd-primary: hsl(172 70.7% 55.9%);
}

main a:focus,
main a:focus-visible,
main button:focus,
main button:focus-visible,
main figure:focus,
main figure:focus-visible {
  box-shadow: 0 0 0 2px var(--color-fd-primary);
  border-radius: var(--radius-lg);
  outline-offset: 0;
  outline: 0;
}

[data-radix-scroll-area-viewport]:focus-visible {
  outline: 0;
  outline-offset: 0;
}
