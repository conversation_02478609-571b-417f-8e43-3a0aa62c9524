import { ImageResponse } from "next/og";

// Image metadata
export const size = {
  width: 32,
  height: 32,
};
export const contentType = "image/png";

// Image generation
export default function Icon() {
  return new ImageResponse(
    // biome-ignore lint/a11y/noSvgWithoutTitle: <explanation>
    <svg
      width="32"
      height="32"
      viewBox="0 0 149 150"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M74.7824 14.3342C69.2018 14.3342 64.6778 18.8582 64.6778 24.4388C64.6778 30.0194 69.2018 34.5434 74.7824 34.5434C80.363 34.5434 84.887 30.0194 84.887 24.4388C84.887 18.8582 80.363 14.3342 74.7824 14.3342ZM51.2051 24.4388C51.2051 11.4174 61.761 0.86145 74.7824 0.86145C87.8038 0.86145 98.3598 11.4174 98.3598 24.4388C98.3598 37.4602 87.8038 48.0161 74.7824 48.0161C70.7222 48.0161 66.9017 46.9898 63.5662 45.1825L45.0035 63.7451C45.769 65.1578 46.3943 66.6574 46.861 68.2253H102.705C105.603 58.4862 114.625 51.3843 125.306 51.3843C138.327 51.3843 148.883 61.9402 148.883 74.9616C148.883 87.983 138.327 98.539 125.306 98.539C114.625 98.539 105.603 91.4371 102.705 81.6981H46.8609C46.2461 83.7639 45.3558 85.711 44.2325 87.4969L62.2474 105.512C65.8784 103.228 70.1762 101.907 74.7824 101.907C87.8038 101.907 98.3598 112.463 98.3598 125.484C98.3598 138.506 87.8038 149.062 74.7824 149.062C61.761 149.062 51.2051 138.506 51.2051 125.484C51.2051 121.978 51.9703 118.651 53.3429 115.661L34.0835 96.4013C31.093 97.7738 27.7658 98.539 24.26 98.539C11.2386 98.539 0.682617 87.983 0.682617 74.9616C0.682617 61.9402 11.2386 51.3843 24.26 51.3843C28.3205 51.3843 32.1412 52.4107 35.4769 54.2183L54.0393 35.656C52.2316 32.3202 51.2051 28.4994 51.2051 24.4388ZM115.201 74.9617L115.201 74.9841C115.213 80.5544 119.733 85.0662 125.306 85.0662C130.886 85.0662 135.41 80.5422 135.41 74.9616C135.41 69.381 130.886 64.857 125.306 64.857C119.733 64.857 115.213 69.3689 115.201 74.9392L115.201 74.9617ZM24.26 64.857C18.6794 64.857 14.1554 69.381 14.1554 74.9616C14.1554 80.5422 18.6794 85.0662 24.26 85.0662C29.8406 85.0662 34.3645 80.5422 34.3645 74.9616C34.3645 69.381 29.8406 64.857 24.26 64.857ZM64.6778 125.484C64.6778 119.904 69.2018 115.38 74.7824 115.38C80.363 115.38 84.887 119.904 84.887 125.484C84.887 131.065 80.363 135.589 74.7824 135.589C69.2018 135.589 64.6778 131.065 64.6778 125.484Z"
        fill="#22d3bb"
      />
    </svg>,

    // ImageResponse options
    {
      // For convenience, we can re-use the exported icons size metadata
      // config to also set the ImageResponse's width and height.
      ...size,
    }
  );
}
