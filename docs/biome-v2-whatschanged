# Upgrade to Biome v2

If you have a project that uses Biome v1, and you want to upgrade to v2, this guide will provide all the information you need.

## Upgrade via CLI

[Section titled “Upgrade via CLI”](https://biomejs.dev/guides/upgrade-to-biome-v2/#upgrade-via-cli)

1.  Use your package manager to install the version `2.0.6` of Biome:
    
    -   [npm](https://biomejs.dev/guides/upgrade-to-biome-v2/#tab-panel-167)
    -   [pnpm](https://biomejs.dev/guides/upgrade-to-biome-v2/#tab-panel-168)
    -   [bun](https://biomejs.dev/guides/upgrade-to-biome-v2/#tab-panel-169)
    -   [deno](https://biomejs.dev/guides/upgrade-to-biome-v2/#tab-panel-170)
    -   [yarn](https://biomejs.dev/guides/upgrade-to-biome-v2/#tab-panel-171)
    
        1npm install --save-dev --save-exact @biomejs/biome@2.0.6
    
        1pnpm add --save-dev --save-exact @biomejs/biome@2.0.6
    
        1bun add --dev --exact @biomejs/biome@2.0.6
    
        1deno add --dev npm:@biomejs/biome@2.0.6
    
        1yarn add --dev --exact @biomejs/biome@2.0.6
    
2.  Run the `migrate` command to update the configuration:
    
    -   [npm](https://biomejs.dev/guides/upgrade-to-biome-v2/#tab-panel-172)
    -   [pnpm](https://biomejs.dev/guides/upgrade-to-biome-v2/#tab-panel-173)
    -   [bun](https://biomejs.dev/guides/upgrade-to-biome-v2/#tab-panel-174)
    -   [deno](https://biomejs.dev/guides/upgrade-to-biome-v2/#tab-panel-175)
    -   [yarn](https://biomejs.dev/guides/upgrade-to-biome-v2/#tab-panel-176)
    
        1npx @biomejs/biome migrate --write
    
        1pnpm exec biome migrate --write
    
        1bunx --bun biome migrate --write
    
        1deno run -A npm:@biomejs/biome migrate --write
    
        1yarn exec biome migrate --write
    
3.  You’re all set! The `migrate` command should have updated your configuration to _mitigate_ possible [breaking changes](https://biomejs.dev/guides/upgrade-to-biome-v2/#breaking-changes). Do check the output of the command however; in some cases it’s possible that you need to perform some manual steps. If so, the migrate command will point them out to you.
    

## Breaking changes

[Section titled “Breaking changes”](https://biomejs.dev/guides/upgrade-to-biome-v2/#breaking-changes)

While the project and team are committed to reducing the number of breaking changes, v2 comes with some breakages that are worth explaining. This section will walk through the most relevant ones, and it will explain the reasons of the breaking change and provide a solution, if applicable.

### Rome-related features aren’t supported anymore

[Section titled “Rome-related features aren’t supported anymore”](https://biomejs.dev/guides/upgrade-to-biome-v2/#rome-related-features-arent-supported-anymore)

All features related to the old Rome project aren’t supported anymore. If you still relied on those features, you must upgrade your project:

-   rename `rome.json` to `biome.json`.
-   rename `// rome-ignore` to `// biome-ignore`.
-   rename `ROME_BINARY` to `BIOME_BINARY`.
-   the suppression comment format `// biome-ignore lint(<GROUP_NAME>/<RULE_NAME>): <explanation>` is no longer supported. use `// biome-ignore lint/<GROUP_NAME>/<RULE_NAME>: <explanation>` instead.

### The option `--config-path` is removed

[Section titled “The option --config-path is removed”](https://biomejs.dev/guides/upgrade-to-biome-v2/#the-option---config-path-is-removed)

The CLI option `--config-path` is removed from the commands `biome lsp-proxy` and `biome start`.

The option was overriding the configuration path for all workspaces opened in the Biome daemon, which led to a configuration mismatch problem when multiple projects are opened in some editors or IDEs.

If you use an LSP-compatible editor, you can use its settings to define the configuration path of the project.

-   [Zed](https://biomejs.dev/guides/upgrade-to-biome-v2/#tab-panel-177)
-   [VS Code](https://biomejs.dev/guides/upgrade-to-biome-v2/#tab-panel-178)

.zed/settings.json

    1{2  "lsp": {3    "biome": {4      "settings": {5         "configuration_path": "./frontend/biome.json"6      }7    }8  }9}

.vscode/settings.json

    1{2  "biome.lsp": {3    "configurationPath": "./frontend/biome.json"4  }5}

If you are a developer of a plugin, please update your plugin to use the `workspace/configuration` response instead of using the `--config-path` argument. Biome’s LSP will resolve a configuration in the workspace automatically, so it is recommended to keep it empty unless you are using a custom configuration path.

### The options `ignore` and `include` are removed, and replaced by `includes`

[Section titled “The options ignore and include are removed, and replaced by includes”](https://biomejs.dev/guides/upgrade-to-biome-v2/#the-options-ignore-and-include-are-removed-and-replaced-by-includes)

If you ran the command `migrate` already, you shouldn’t have regressions. The reason why these two fields were merged into a single new one was because the implementation of Biome’s old glob engine was flawed, causing some edge cases that we couldn’t fix unless we changed our engine. Unfortunately, we couldn’t do this without breaking changes.

The previous glob engine would prepend the matcher `**/`to any user-provided glob. Which means that the glob `src/**` was always converted to `**/src/**`, causing some unexpected behavior in some cases:

`/projectA/src/file.js`

`src/file.js`

`/projectB/frontend/src/file.js`

`src/**`

Glob _doesn’t_ match path

Glob matches path

Glob _doesn’t_ match path

`**/src/**`

Glob matches path

Glob matches path

Glob matches

As you can see, the glob `**/src/**` is **too** eager, and it matches paths that shouldn’t have, for example `/Users/<USER>/projectB/frontend/src/file.js`.

From v2, Biome **won’t prepend `**/`** to globs anymore.

In the previous glob engine the pattern `*` matched against any sequence of characters, including the path separator `/`. Which means that the glob `**/src/*.js` was always converted to `**/src/**/*.js`. From v2, Biome `*` **won’t match the path separator `/`**.

### Paths and globs are now relative to the configuration file

[Section titled “Paths and globs are now relative to the configuration file”](https://biomejs.dev/guides/upgrade-to-biome-v2/#paths-and-globs-are-now-relative-to-the-configuration-file)

In v1, globs and files declared in the configuration file were relative to the working directory. This behavior could cause some unexpected behaviour, especially if coming from other tools.

In the following example, the configuration file is at root of the project, the `check` command is run from a subdirectory and configured to analyze only files inside the `src/` folder.

-   biome.json
-   Directorysrc/
    
    -   deploy.js
    
-   Directoryui/
    
    -   package.json
    -   Directorysrc/
        
        -   main.tsx
        -   utils.ts
        
    

ui/package.json

    1{2  "name": "@org/ui",3  "publish": false,4  "scripts": {5    "check": "biome check"6  }7}

biome.json

    1{2  "files": {3    "includes": ["src/**"]4  }5}

In **v1**, when you run `npm run check`, the following happens:

-   Biome looks for `biome.json` inside `ui/`
-   No configuration file found, Biome starts checking the parent folders
-   Biome finds `biome.json` **in the parent folder**
-   The _working directory_ is `ui/`, because that’s where the CLI commands was ran
-   Biome prepend `ui/` to `src/**`
-   The glob `ui/src/**` matches `ui/src/main.tsx` and `ui/src/utils.ts`, but it doesn’t match `src/deploy.js`
-   Two files are analyzed

In **v2**, when you run `npm run check`, the following happens:

-   Biome looks for `biome.json` inside `ui/`
-   No configuration file found, Biome starts checking the parent folders
-   Biome finds `biome.json` **in the parent folder**
-   The glob `src/**` doesn’t match `ui/src/main.tsx` and `ui/src/utils.ts`, but matches `src/deploy.js`
-   One file is analyzed

To match the previous behavior, the glob must be updated to `ui/src/**`:

biome.json

    1{2  "files": {3    "includes": ["ui/src/**"]4  }5}

### The option `all` is removed from the linter

[Section titled “The option all is removed from the linter”](https://biomejs.dev/guides/upgrade-to-biome-v2/#the-option-all-is-removed-from-the-linter)

In the very early versions of Biome, we introduced `all` as a mean to enable all rules of the linter, or enable all rules that belong to a group. At that time, Biome had few rules, and their maintenance cost was very low to the maintainers and the end-users. We didn’t think twice, and we just added it.

Now Biome has more than 300 rules, and some of those rules _conflict with each other_, causing situations where a project can’t fix the violations of the rules, because one fix triggers another rule, and vice versa.

We decided to **take a step back**, remove the option, and introduce it in the future in some other form, maybe with different semantics and configuration. We are aware that this breaking change can cause some inconvenience.

The maintainers of the project are already discussing the topic on Discord and [GitHub](https://github.com/biomejs/biome/issues/5764). You are encouraged to take part in the conversation and help us find a good solution.

As _limited_ workaround, you can enable the recommended rules, and enable all the linter domains. However, you can’t disable single rules, and the domains `react` and `solid` enable rules that conflict with each other:

biome.json

    1{2  "linter": {3    "domains": {4      "next": "all",5      "react": "all",6      "test": "all",7      "solid": "all",8      "project": "all"9    },10    "rules": {11      "recommended": true12    }13  }14}

### Syntax `assert` no longer supported

[Section titled “Syntax assert no longer supported”](https://biomejs.dev/guides/upgrade-to-biome-v2/#syntax-assert-no-longer-supported)

The `assert` syntax, which reached Stage 3, is no longer supported, and it was replaced by the `with` syntax.

All LTS versions of Node.js support the new syntax, as well as all browser engines and serverless runtimes.

    1import {test} from "foo.json" assert { for: "for" }2export * from "mod" assert { type: "json" }3import {test} from "foo.json" with { for: "for" }4export * from "mod" with { type: "json" }

### The linter works differently

[Section titled “The linter works differently”](https://biomejs.dev/guides/upgrade-to-biome-v2/#the-linter-works-differently)

In v1, the linter worked as follows:

-   The recommended rules, by default, emit only diagnostics with error.
-   The non-recommended rules needed to be manually enabled, and the user had to decide the severity.

This way of working was a bit different from other linters (ESLint, clippy, golint, etc.), and it was very limited.

In v2, the linter works like other linters do, which means the following:

-   Every rule is associated with a default severity level, suggested by Biome.
-   The recommended rules can emit diagnostics of different severity.
-   Users can now avail of the default severity of a rule, or choose the severity themselves.

If you relied on recommended rules to always emit an error, the `biome migrate` command will set the severity of those rules to `"error"`.

### `style` rules don’t emit errors anymore

[Section titled “style rules don’t emit errors anymore”](https://biomejs.dev/guides/upgrade-to-biome-v2/#style-rules-dont-emit-errors-anymore)

We received a lot of great feedback in the past months regarding our recommended rules. We realized that it’s challenging to balance a “recommended” set of rules for our users. While some really liked our defaults, others thought that they were too noisy.

From v2, all rules that belong to the `style` group won’t emit an error unless configured otherwise. The `biome migrate` command will update the configuration so they still emit errors if you had them enabled. Make sure that the configuration conforms to your standards.

### Different default formatting of `package.json`

[Section titled “Different default formatting of package.json”](https://biomejs.dev/guides/upgrade-to-biome-v2/#different-default-formatting-of-packagejson)

Biome now formats `package.json` with different defaults. Now objects and arrays are always formatted on multiple lines, regardless of their width:

package.json

    1{ "name": "project", "dependencies": { "foo": "^1.0.0" } }2{3 "name": "project",4 "dependencies": {5   "foo": "^1.0.0"6 }7}

If you liked the previous formatting style, you’ll have to add an override to the configuration, and use the `"auto"` as value of the `expand` option:

biome.json

    1{2  "overrides": [{3    "includes": ["package.json"],4    "json": {5      "formatter": {6        "expand": "auto"7      }8    }9  }]10}

### The import organizer sorts differently the imports

[Section titled “The import organizer sorts differently the imports”](https://biomejs.dev/guides/upgrade-to-biome-v2/#the-import-organizer-sorts-differently-the-imports)

Biome v2 comes with a new import organizer with many new features:

-   It supports customizing the sorting.
-   It organizes `export` statements.
-   It ignores blank lines between `import` statements.
-   It merges `import`/`export`.
-   Its default sorting is more consistent than before.

All these changes can lead to different sorting of the imports and exports of your project. The configuration has also been moved from a dedicated `organizeImports` field to an [assist action](https://biomejs.dev/assist/). `biome migrate` takes care of moving the configuration as follows:

biome.json

    1{2   "organizeImports": { "enabled": true }3   "assist": { "actions": { "source": { "organizeImports": "on" } } }4}

One significant difference between the old and new default sorters is that Node.js modules without the `node:` protocol are no longer placed before other imports. For example, the following imports were sorted in Biome 1.x:

    1import fs from "node:fs";2import path from "path";3import pkg from "@a/package";

In Biome 2.0, they are sorted as follows:

    1import fs from "node:fs";2import pkg from "@a/package";3import path from "path";

To restore the old behavior, use a custom order as follows:

biome.jsonc

    1{2    "assist": {3        "actions": {4            "source": {5                "organizeImports": {6                    "level": "on",7                    "options": {8                        "groups": [9                            // Bun modules10                            ":BUN:",11                            // Node.js modules12                            ":NODE:",13                            // Modules imported with the `npm:` protocol14                            ["npm:*", "npm:*/**"],15                            // Modules imported with another protocol. e.g. `jsr:`16                            ":PACKAGE_WITH_PROTOCOL:",17                            // URLs18                            ":URL:",19                            // Libraries20                            ":PACKAGE:",21                            // Absolute paths22                            ["/**"],23                            // Sharp aliases24                            ["#*", "#*/**"],25                            // All other paths26                            ":PATH:"27                        ]28                    }29                }30            }31        }32    }33}

Please note that it is not possible to achieve exactly the same behavior as Biome v1.x. We recommend using the new default, which is much more consistent and simpler.

Read [the documentation of the import organizer](https://biomejs.dev/assist/actions/organize-imports/) for more details.

### Zed extension `v0.2.0` isn’t compatible with v1

[Section titled “Zed extension v0.2.0 isn’t compatible with v1”](https://biomejs.dev/guides/upgrade-to-biome-v2/#zed-extension-v020-isnt-compatible-with-v1)

The new version of the Zed extension isn’t compatible with Biome v1, because `--config-path` isn’t supported anymore. The team tried to maintain backwards compatibility, but unfortunately Zed has very limited debugging capabilities for extension authors.

### VS Code extension v3 needs complete restart

[Section titled “VS Code extension v3 needs complete restart”](https://biomejs.dev/guides/upgrade-to-biome-v2/#vs-code-extension-v3-needs-complete-restart)

While this isn’t directly related to Biome v2, the new version of the VS Code extension uses a different method to connect to the Biome Daemon. If you upgrade the extension, it’s possible you might experience **incorrect code** upon save if you use `"source.fixAll.biome": "explicit"`. To fix this, you need to:

1.  upgrade the extension;
2.  close the editor;
3.  kill possible dangling `biome` processes;
4.  restart the editor;