{
  "cline.modelSettings.o3Mini.reasoningEffort": "high",

  // ESLint Configuration
  "eslint.enable": true,
  "eslint.format.enable": true,
  "eslint.lintTask.enable": true,
  "eslint.run": "onSave",
  "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"],
  "eslint.workingDirectories": [
    {
      "mode": "auto"
    }
  ],
  "eslint.useFlatConfig": true,
  "eslint.codeActionsOnSave.rules": ["*"],

  // Editor Configuration for AI Assistance
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.formatOnType": false,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit",
    "source.removeUnusedImports": "explicit"
  },

  // Default Formatter Settings
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },

  // TypeScript Configuration
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "typescript.suggest.completeFunctionCalls": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.inlayHints.parameterNames.enabled": "all",
  "typescript.inlayHints.variableTypes.enabled": true,
  "typescript.inlayHints.functionLikeReturnTypes.enabled": true,

  // Next.js specific settings
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },

  // File Explorer and Editor
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.patterns": {
    "*.ts": "${capture}.js",
    "*.js": "${capture}.js.map, ${capture}.min.js, ${capture}.d.ts",
    "*.jsx": "${capture}.js",
    "*.tsx": "${capture}.ts",
    "tsconfig.json": "tsconfig.*.json",
    "package.json": "package-lock.json, pnpm-lock.yaml, yarn.lock",
    ".eslintrc.*": ".eslintignore",
    "tailwind.config.*": "tailwind.*.config.*",
    "next.config.*": "next-env.d.ts"
  },

  // Search and IntelliSense
  "search.exclude": {
    "**/node_modules": true,
    "**/.next": true,
    "**/.vercel": true,
    "**/dist": true,
    "**/build": true,
    "**/.turbo": true,
    "**/coverage": true
  },
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/.next/**": true,
    "**/.vercel/**": true,
    "**/dist/**": true,
    "**/build/**": true,
    "**/.turbo/**": true
  },

  // AI-Friendly Display Settings
  "editor.minimap.enabled": true,
  "editor.minimap.maxColumn": 120,
  "editor.rulers": [80, 120],
  "editor.wordWrap": "bounded",
  "editor.wordWrapColumn": 120,
  "editor.lineNumbers": "on",
  "editor.renderWhitespace": "boundary",
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": true,
  "editor.inlineSuggest.enabled": true,

  // Problem Handling
  "problems.showCurrentInStatus": true,
  "problems.sortOrder": "severity",

  // Git Integration
  "git.enableCommitSigning": false,
  "git.autofetch": true,
  "git.enableSmartCommit": true,

  // Performance
  "extensions.experimental.affinity": {
    "asvetliakov.vscode-neovim": "1"
  },

  // Terminal
  "terminal.integrated.defaultProfile.osx": "zsh",
  "terminal.integrated.fontSize": 11,

  // Auto-save for better AI workflow
  "files.autoSave": "onFocusChange",
  "files.autoSaveDelay": 1000,

  // File Associations
  "files.associations": {
    "*.js": "javascript",
    "*.jsx": "javascriptreact",
    "*.ts": "typescript",
    "*.tsx": "typescriptreact"
  }
}
