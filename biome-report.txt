
> my-v0-project@0.1.0 check /Users/<USER>/Documents/GitHub/ponyclub-v0
> biome check --write --max-diagnostics=2000 .

app/[locale]/privacy-settings/PrivacySettingsClient.tsx:17:11 lint/correctness/noUnusedVariables ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  ! This variable consents is unused.
  
    15 │ function _C15tPrivacyDialog({ locale }: { locale: string }) {
    16 │   const consentManager = useConsentManager();
  > 17 │   const { consents } = consentManager;
       │           ^^^^^^^^
    18 │ 
    19 │   // Monitor consent changes and sync to legacy format
  
  i Unused variables are often the result of an incomplete refactoring, typos, or other sources of bugs.
  

components/client/BookingButton.tsx:33:29 lint/correctness/useExhaustiveDependencies  FIXABLE  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  ! This hook does not specify its dependency on conversionLabel.
  
    31 │ }: BookingButtonProps) {
    32 │   // Comprehensive tracking function for analytics
  > 33 │   const trackBookingClick = useCallback(() => {
       │                             ^^^^^^^^^^^
    34 │     // Extract numeric price for conversion tracking
    35 │     const numericPrice =
  
  i This dependency is being used here, but is not specified in the hook dependency list.
  
    38 │     // Determine conversion label based on package type and source page
  > 39 │     let finalConversionLabel = conversionLabel;
       │                                ^^^^^^^^^^^^^^^
    40 │ 
    41 │     if (!finalConversionLabel && packageType && sourcePage) {
  
  i Unsafe fix: Add the missing dependency to the list.
  
    71 │ ··},·[trackingLabel,·packageName,·packagePrice,·id,·conversionLabel]);
       │                                                   +++++++++++++++++   

components/client/BookingButton.tsx:33:29 lint/correctness/useExhaustiveDependencies  FIXABLE  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  ! This hook does not specify its dependency on packageType.toUpperCase.
  
    31 │ }: BookingButtonProps) {
    32 │   // Comprehensive tracking function for analytics
  > 33 │   const trackBookingClick = useCallback(() => {
       │                             ^^^^^^^^^^^
    34 │     // Extract numeric price for conversion tracking
    35 │     const numericPrice =
  
  i This dependency is being used here, but is not specified in the hook dependency list.
  
    44 │         sourcePage === "homepage"
    45 │           ? `NEXT_PUBLIC_ADS_LABEL_HOMEPAGE_${packageType.toUpperCase()}`
  > 46 │           : `NEXT_PUBLIC_ADS_LABEL_${packageType.toUpperCase()}`;
       │                                      ^^^^^^^^^^^^^^^^^^^^^^^
    47 │ 
    48 │       finalConversionLabel = process.env[labelKey as keyof typeof process.env];
  
  i This dependency is being used here, but is not specified in the hook dependency list.
  
    43 │       const labelKey =
    44 │         sourcePage === "homepage"
  > 45 │           ? `NEXT_PUBLIC_ADS_LABEL_HOMEPAGE_${packageType.toUpperCase()}`
       │                                               ^^^^^^^^^^^^^^^^^^^^^^^
    46 │           : `NEXT_PUBLIC_ADS_LABEL_${packageType.toUpperCase()}`;
    47 │ 
  
  i Unsafe fix: Add the missing dependency to the list.
  
    71 │ ··},·[trackingLabel,·packageName,·packagePrice,·id,·packageType.toUpperCase]);
       │                                                   +++++++++++++++++++++++++   

components/client/BookingButton.tsx:33:29 lint/correctness/useExhaustiveDependencies  FIXABLE  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  ! This hook does not specify its dependency on packageType.
  
    31 │ }: BookingButtonProps) {
    32 │   // Comprehensive tracking function for analytics
  > 33 │   const trackBookingClick = useCallback(() => {
       │                             ^^^^^^^^^^^
    34 │     // Extract numeric price for conversion tracking
    35 │     const numericPrice =
  
  i This dependency is being used here, but is not specified in the hook dependency list.
  
    61 │       conversionLabel: finalConversionLabel,
    62 │       sourcePage,
  > 63 │       packageType,
       │       ^^^^^^^^^^^
    64 │     });
    65 │ 
  
  i This dependency is being used here, but is not specified in the hook dependency list.
  
    39 │     let finalConversionLabel = conversionLabel;
    40 │ 
  > 41 │     if (!finalConversionLabel && packageType && sourcePage) {
       │                                  ^^^^^^^^^^^
    42 │       // Generate conversion label based on package type and source page
    43 │       const labelKey =
  
  i Unsafe fix: Add the missing dependency to the list.
  
     69  69 │         );
     70  70 │       }
     71     │ - ··},·[trackingLabel,·packageName,·packagePrice,·id]);
         71 │ + ··},·[trackingLabel,·packageName,·packagePrice,·id,·{
         72 │ + ······packageName,
         73 │ + ······packagePrice:·numericPrice,
         74 │ + ······buttonId:·id,
         75 │ + ······trackingLabel:·`${trackingLabel}·-·${sourcePage}`,
         76 │ + ······conversionLabel:·finalConversionLabel,
         77 │ + ······sourcePage,
         78 │ + ······packageType,
         79 │ + ····}]);
     72  80 │   
     73  81 │     const handleBookNowClick = () => {
  

components/client/BookingButton.tsx:33:29 lint/correctness/useExhaustiveDependencies  FIXABLE  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  ! This hook does not specify its dependency on sourcePage.
  
    31 │ }: BookingButtonProps) {
    32 │   // Comprehensive tracking function for analytics
  > 33 │   const trackBookingClick = useCallback(() => {
       │                             ^^^^^^^^^^^
    34 │     // Extract numeric price for conversion tracking
    35 │     const numericPrice =
  
  i This dependency is being used here, but is not specified in the hook dependency list.
  
    60 │       trackingLabel: `${trackingLabel} - ${sourcePage}`,
    61 │       conversionLabel: finalConversionLabel,
  > 62 │       sourcePage,
       │       ^^^^^^^^^^
    63 │       packageType,
    64 │     });
  
  i This dependency is being used here, but is not specified in the hook dependency list.
  
    58 │       packagePrice: numericPrice,
    59 │       buttonId: id,
  > 60 │       trackingLabel: `${trackingLabel} - ${sourcePage}`,
       │                                            ^^^^^^^^^^
    61 │       conversionLabel: finalConversionLabel,
    62 │       sourcePage,
  
  i This dependency is being used here, but is not specified in the hook dependency list.
  
    39 │     let finalConversionLabel = conversionLabel;
    40 │ 
  > 41 │     if (!finalConversionLabel && packageType && sourcePage) {
       │                                                 ^^^^^^^^^^
    42 │       // Generate conversion label based on package type and source page
    43 │       const labelKey =
  
  i This dependency is being used here, but is not specified in the hook dependency list.
  
    42 │       // Generate conversion label based on package type and source page
    43 │       const labelKey =
  > 44 │         sourcePage === "homepage"
       │         ^^^^^^^^^^
    45 │           ? `NEXT_PUBLIC_ADS_LABEL_HOMEPAGE_${packageType.toUpperCase()}`
    46 │           : `NEXT_PUBLIC_ADS_LABEL_${packageType.toUpperCase()}`;
  
  i Unsafe fix: Add the missing dependency to the list.
  
     69  69 │         );
     70  70 │       }
     71     │ - ··},·[trackingLabel,·packageName,·packagePrice,·id]);
         71 │ + ··},·[trackingLabel,·packageName,·packagePrice,·id,·{
         72 │ + ······packageName,
         73 │ + ······packagePrice:·numericPrice,
         74 │ + ······buttonId:·id,
         75 │ + ······trackingLabel:·`${trackingLabel}·-·${sourcePage}`,
         76 │ + ······conversionLabel:·finalConversionLabel,
         77 │ + ······sourcePage,
         78 │ + ······packageType,
         79 │ + ····}]);
     72  80 │   
     73  81 │     const handleBookNowClick = () => {
  

components/client/ConsentBridge.tsx:207:5 lint/correctness/useExhaustiveDependencies ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  ! 
        handleConsentRevocation changes on every re-render and should not be used as a hook dependency.
  
    205 │     consents,
    206 │     hasConsentFor,
  > 207 │     handleConsentRevocation,
        │     ^^^^^^^^^^^^^^^^^^^^^^^
    208 │     readLegacyCookie, // Update the legacy cookie format (existing functionality)
    209 │     updateLegacyCookie,
  
  i To fix this, wrap the definition of 
        handleConsentRevocation in its own useCallback() hook.
  

components/client/ConsentBridge.tsx:208:5 lint/correctness/useExhaustiveDependencies ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  ! 
        readLegacyCookie changes on every re-render and should not be used as a hook dependency.
  
    206 │     hasConsentFor,
    207 │     handleConsentRevocation,
  > 208 │     readLegacyCookie, // Update the legacy cookie format (existing functionality)
        │     ^^^^^^^^^^^^^^^^
    209 │     updateLegacyCookie,
    210 │   ]);
  
  i To fix this, wrap the definition of 
        readLegacyCookie in its own useCallback() hook.
  

components/client/ConsentBridge.tsx:209:5 lint/correctness/useExhaustiveDependencies ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  ! 
        updateLegacyCookie changes on every re-render and should not be used as a hook dependency.
  
    207 │     handleConsentRevocation,
    208 │     readLegacyCookie, // Update the legacy cookie format (existing functionality)
  > 209 │     updateLegacyCookie,
        │     ^^^^^^^^^^^^^^^^^^
    210 │   ]);
    211 │ 
  
  i To fix this, wrap the definition of 
        updateLegacyCookie in its own useCallback() hook.
  

components/client/ConsentBridge.tsx:238:5 lint/correctness/useExhaustiveDependencies ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  ! 
        readLegacyCookie changes on every re-render and should not be used as a hook dependency.
  
    236 │   }, [
    237 │     hasConsentFor,
  > 238 │     readLegacyCookie, // Note: In a real implementation, you would use c15t's API to update consent
        │     ^^^^^^^^^^^^^^^^
    239 │     // For now, we'll let c15t be the source of truth and update the legacy cookie
    240 │     updateLegacyCookie,
  
  i To fix this, wrap the definition of 
        readLegacyCookie in its own useCallback() hook.
  

components/client/ConsentBridge.tsx:240:5 lint/correctness/useExhaustiveDependencies ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  ! 
        // For now, we'll let c15t be the source of truth and update the legacy cookie
        updateLegacyCookie changes on every re-render and should not be used as a hook dependency.
  
    238 │     readLegacyCookie, // Note: In a real implementation, you would use c15t's API to update consent
    239 │     // For now, we'll let c15t be the source of truth and update the legacy cookie
  > 240 │     updateLegacyCookie,
        │     ^^^^^^^^^^^^^^^^^^
    241 │   ]);
    242 │ 
  
  i To fix this, wrap the definition of 
        // For now, we'll let c15t be the source of truth and update the legacy cookie
        updateLegacyCookie in its own useCallback() hook.
  

Skipped 4 suggested fixes.
If you wish to apply the suggested (unsafe) fixes, use the command biome check --write --unsafe

Checked 121 files in 44ms. No fixes applied.
Found 10 warnings.
