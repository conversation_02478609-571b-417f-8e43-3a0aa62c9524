# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
codebase.md
public/images/.DS_Store
public/.DS_Store
.DS_Store
fixing-process.md
magicui-mcp-learnings.md

# Sentry Config File
.env.sentry-build-plugin


